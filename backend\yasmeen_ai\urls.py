"""
URL configuration for yasmeen_ai project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
from django.utils.translation import gettext_lazy as _
# from ai_recommendations.admin import ai_admin_site  # REMOVED - deprecated

# Non-i18n patterns - API endpoints should not have language prefixes
urlpatterns = [
    # Language switch URL
    path('i18n/', include('django.conf.urls.i18n')),
    # API endpoints (no language prefix needed)
    path('api/auth/', include('rest_framework.urls')),
    path('api/users/', include('users.urls')),
    path('api/search/', include('search.urls')),
    path('api/incubator/', include('incubator.urls')),
    # path('api/ai-recommendations/', include('ai_recommendations.urls')),  # DISABLED - app disabled
    # path('api/ai/', include('core.ai_urls')),  # DISABLED - may cause issues
    path('api/forums/', include('forums.urls')),
    path('api/', include('api.urls')),
]

# i18n patterns - these URLs will have language prefix (only admin and frontend routes)
urlpatterns += i18n_patterns(
    path(_('admin/'), admin.site.urls),
    # path(_('ai-admin/'), ai_admin_site.urls),  # AI-specific admin dashboard - REMOVED (deprecated)
    # Add prefix_default_language=False to avoid having /en/ prefix for default language
    prefix_default_language=False
)

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
