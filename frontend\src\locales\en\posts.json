{"posts": {"title": "Title", "content": "Content", "excerpt": "Excerpt", "type": "Type", "category": "Category", "author": "Author", "engagement": "Engagement", "status": "Status", "totalPosts": "Total Posts", "published": "Published", "featured": "Featured", "posts": "Posts", "managePosts": "Create and manage community posts", "createPost": "Create Post", "editPost": "Edit Post", "deletePost": "Delete Post", "viewPost": "View Post", "noPosts": "No posts found. Create your first post to get started!", "allPosts": "All Posts", "searchPosts": "Search posts...", "myPosts": "My Posts", "draftPosts": "Draft Posts", "publishedPosts": "Published Posts", "featuredPosts": "Featured Posts", "recentPosts": "Recent Posts", "popularPosts": "Popular Posts", "trendingPosts": "Trending Posts", "postDetails": "Post Details", "postSettings": "Post Settings", "postAnalytics": "Post Analytics", "postComments": "Post Comments", "postShares": "Post Shares", "postLikes": "Post Likes", "postViews": "Post Views", "postEngagement": "Post Engagement", "postPerformance": "Post Performance", "postInsights": "Post Insights", "postMetrics": "Post Metrics", "postStatistics": "Post Statistics", "postReports": "Post Reports", "postHistory": "Post History", "postVersions": "Post Versions", "postRevisions": "Post Revisions", "postBackups": "Post Backups", "postArchive": "Post Archive", "postTrash": "Post Trash", "postRestore": "Restore Post", "postPermanentDelete": "Permanently Delete Post", "postDuplicate": "Duplicate Post", "postClone": "Clone Post", "postTemplate": "Post Template", "postSchedule": "Schedule Post", "postPublish": "Publish Post", "postUnpublish": "Unpublish Post", "postDraft": "Save as Draft", "postPreview": "Preview Post", "postShare": "Share Post", "postEmbed": "Embed Post", "postExport": "Export Post", "postImport": "Import Post", "postBulkActions": "Bulk Actions", "postBulkEdit": "Bulk Edit", "postBulkDelete": "Bulk Delete", "postBulkPublish": "Bulk Publish", "postBulkUnpublish": "Bulk Unpublish", "postBulkFeature": "Bulk Feature", "postBulkUnfeature": "Bulk Unfeature", "postBulkArchive": "Bulk Archive", "postBulkRestore": "Bulk Restore", "postBulkMove": "Bulk Move", "postBulkCopy": "Bulk Copy", "postBulkExport": "Bulk Export", "postBulkImport": "Bulk Import", "titlePlaceholder": "Enter post title", "excerptPlaceholder": "Provide a brief summary of the post", "selectCategory": "Select a category", "contentPlaceholder": "Write your post content here...", "featuredImage": "Featured Image URL", "featuredImageHelp": "Optional: URL to an image for this post", "tagsPlaceholder": "startup, business, tips", "tagsHelp": "Separate tags with commas", "isFeatured": "Featured post", "featuredHelp": "Featured posts appear prominently in listings", "isPublished": "Published", "publishedHelp": "Only published posts are visible to users", "allowComments": "Allow comments", "commentsHelp": "Users can comment on this post", "types": {"article": "Article", "news": "News", "blog": "Blog Post", "announcement": "Announcement", "update": "Update", "tutorial": "Tutorial", "discussion": "Discussion", "question": "Question", "guide": "Guide", "review": "Review", "interview": "Interview", "case_study": "Case Study", "whitepaper": "Whitepaper", "research": "Research", "opinion": "Opinion", "editorial": "Editorial", "feature": "Feature", "story": "Story", "report": "Report", "analysis": "Analysis", "commentary": "Commentary"}, "categories": {"general": "General", "technology": "Technology", "business": "Business", "entrepreneurship": "Entrepreneurship", "startup": "Startup", "innovation": "Innovation", "marketing": "Marketing", "finance": "Finance", "investment": "Investment", "funding": "Funding", "growth": "Growth", "strategy": "Strategy", "leadership": "Leadership", "management": "Management", "productivity": "Productivity", "education": "Education", "training": "Training", "development": "Development", "research": "Research", "industry": "Industry", "trends": "Trends", "insights": "Insights", "tips": "Tips", "advice": "Advice", "best_practices": "Best Practices", "case_studies": "Case Studies", "success_stories": "Success Stories", "lessons_learned": "Lessons Learned", "challenges": "Challenges", "solutions": "Solutions", "tools": "Tools", "resources": "Resources", "guides": "Guides", "tutorials": "Tutorials", "how_to": "How To", "news": "News", "updates": "Updates", "announcements": "Announcements", "events": "Events", "community": "Community", "networking": "Networking", "collaboration": "Collaboration", "partnerships": "Partnerships", "mentorship": "Mentorship", "coaching": "Coaching", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "reviews": "Reviews", "testimonials": "Testimonials", "interviews": "Interviews", "profiles": "Profiles", "spotlights": "Spotlights", "features": "Features", "stories": "Stories", "experiences": "Experiences", "journeys": "Journeys", "milestones": "Milestones", "achievements": "Achievements", "awards": "Awards", "recognition": "Recognition", "celebrations": "Celebrations"}, "statuses": {"draft": "Draft", "pending": "Pending Review", "published": "Published", "scheduled": "Scheduled", "private": "Private", "password_protected": "Password Protected", "archived": "Archived", "trashed": "Trashed", "featured": "Featured", "pinned": "Pinned", "sticky": "<PERSON>y", "locked": "Locked", "hidden": "Hidden", "suspended": "Suspended", "flagged": "Flagged", "reported": "Reported", "under_review": "Under Review", "approved": "Approved", "rejected": "Rejected", "expired": "Expired", "inactive": "Inactive", "active": "Active"}, "filters": {"all": "All Posts", "my": "My Posts", "published": "Published", "draft": "Drafts", "scheduled": "Scheduled", "featured": "Featured", "popular": "Popular", "recent": "Recent", "trending": "Trending", "top_rated": "Top Rated", "most_viewed": "Most Viewed", "most_liked": "Most Liked", "most_shared": "Most Shared", "most_commented": "Most Commented"}, "sorting": {"newest": "Newest First", "oldest": "Oldest First", "title_asc": "Title A-Z", "title_desc": "Title Z-A", "author_asc": "Author A-Z", "author_desc": "Author Z-<PERSON>", "views_desc": "Most Viewed", "views_asc": "Least Viewed", "likes_desc": "Most Liked", "likes_asc": "Least Liked", "comments_desc": "Most Commented", "comments_asc": "Least Commented", "shares_desc": "Most Shared", "shares_asc": "Least Shared", "engagement_desc": "Highest Engagement", "engagement_asc": "Lowest Engagement"}, "actions": {"create": "Create Post", "edit": "Edit Post", "delete": "Delete Post", "view": "View Post", "publish": "Publish", "unpublish": "Unpublish", "feature": "Feature", "unfeature": "Unfeature", "pin": "<PERSON>n", "unpin": "Unpin", "lock": "Lock", "unlock": "Unlock", "archive": "Archive", "restore": "Rest<PERSON>", "duplicate": "Duplicate", "share": "Share", "embed": "Embed", "export": "Export", "report": "Report", "flag": "Flag", "bookmark": "Bookmark", "like": "Like", "unlike": "Unlike", "comment": "Comment", "reply": "Reply", "quote": "Quote", "repost": "Repost", "save": "Save", "print": "Print", "download": "Download"}, "messages": {"created": "Post created successfully", "updated": "Post updated successfully", "deleted": "Post deleted successfully", "published": "Post published successfully", "unpublished": "Post unpublished successfully", "featured": "Post featured successfully", "unfeatured": "Post unfeatured successfully", "archived": "Post archived successfully", "restored": "Post restored successfully", "duplicated": "Post duplicated successfully", "shared": "Post shared successfully", "bookmarked": "Post bookmarked successfully", "liked": "Post liked successfully", "unliked": "Post unliked successfully", "reported": "Post reported successfully", "flagged": "Post flagged successfully", "error_create": "Failed to create post", "error_update": "Failed to update post", "error_delete": "Failed to delete post", "error_publish": "Failed to publish post", "error_load": "Failed to load posts", "error_share": "Failed to share post", "error_bookmark": "Failed to bookmark post", "error_like": "Failed to like post", "error_report": "Failed to report post", "confirm_delete": "Are you sure you want to delete this post?", "confirm_bulk_delete": "Are you sure you want to delete {{count}} posts?", "confirm_publish": "Are you sure you want to publish this post?", "confirm_unpublish": "Are you sure you want to unpublish this post?", "confirm_archive": "Are you sure you want to archive this post?", "unsaved_changes": "You have unsaved changes. Are you sure you want to leave?"}, "validation": {"title_required": "Title is required", "title_min_length": "Title must be at least {{min}} characters", "title_max_length": "Title must be no more than {{max}} characters", "content_required": "Content is required", "content_min_length": "Content must be at least {{min}} characters", "content_max_length": "Content must be no more than {{max}} characters", "excerpt_max_length": "Excerpt must be no more than {{max}} characters", "category_required": "Category is required", "type_required": "Post type is required", "invalid_format": "Invalid format", "invalid_url": "Invalid URL", "invalid_image": "Invalid image format", "file_too_large": "File size is too large", "unsupported_format": "Unsupported file format"}}}