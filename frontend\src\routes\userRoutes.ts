import { lazy } from 'react';
import { RouteConfig, createUserRoute, createAIRoute } from './routeConfig';

// DEDICATED USER DASHBOARD - Regular users only
const UserDashboardPage = lazy(() => import('../pages/dashboard/UserDashboardPage'));
// Legacy UserDashboard kept for backward compatibility
const UserDashboard = lazy(() => import('../components/dashboard/user-dashboard/UserDashboard'));

// Fixed syntax error in BusinessIdeasPage - now loading directly
const BusinessIdeasPage = lazy(() => import('../pages/dashboard/BusinessIdeasPage'));

const BusinessPlanPage = lazy(() => import('../pages/dashboard/BusinessPlanPage'));
const TemplatesOverviewPage = lazy(() => import('../pages/dashboard/TemplatesOverviewPage'));
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const ResourcesPage = lazy(() => import('../pages/dashboard/ResourcesPage'));
const UserAnalyticsPage = lazy(() => import('../pages/dashboard/UserAnalyticsPage'));
const EnhancedAnalyticsPage = lazy(() => import('../pages/dashboard/EnhancedAnalyticsPage'));
const UserProfilePage = lazy(() => import('../pages/UserProfilePage'));
const BusinessIncubator = lazy(() => import('../components/incubator/BusinessIncubator'));
const UserSettings = lazy(() => import('../pages/UserSettings'));
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));

/**
 * ✅ ESSENTIAL USER ROUTES - Core working pages that match sidebar
 */
export const userRoutes: RouteConfig[] = [
  // Main dashboard - DEDICATED USER ONLY ✅ (using UserDashboardPage)
  createUserRoute('/dashboard', UserDashboardPage, 'Loading dashboard...'),

  // Business ideas - VERIFIED ✅
  createUserRoute('/dashboard/business-ideas', BusinessIdeasPage, 'Loading business ideas...'),
  createUserRoute('/dashboard/business-ideas/new', BusinessIdeasPage, 'Loading business idea creator...'),

  // Business plans - NEW ✅
  createUserRoute('/dashboard/business-plans', BusinessPlanPage, 'Loading business plans...'),
  createUserRoute('/dashboard/business-plans/new', BusinessPlanPage, 'Loading business plan creator...'),
  createUserRoute('/dashboard/business-plans/:id', BusinessPlanPage, 'Loading business plan...'),

  // Templates - VERIFIED ✅
  createUserRoute('/dashboard/templates', TemplatesOverviewPage, 'Loading templates...'),

  // Posts - VERIFIED ✅
  createUserRoute('/dashboard/posts', PostsPage, 'Loading posts...'),

  // Events - VERIFIED ✅
  createUserRoute('/dashboard/events', EventsPage, 'Loading events...'),

  // Resources - VERIFIED ✅
  createUserRoute('/dashboard/resources', ResourcesPage, 'Loading resources...'),

  // Analytics - NEW ✅
  createUserRoute('/dashboard/analytics', UserAnalyticsPage, 'Loading analytics...'),

  // Enhanced Analytics for specific business ideas - NEW ✅
  createUserRoute('/dashboard/analytics/:id', EnhancedAnalyticsPage, 'Loading enhanced analytics...'),

  // Profile - VERIFIED ✅
  createUserRoute('/profile', UserProfilePage, 'Loading profile...'),

  // Incubator - VERIFIED ✅ (public route /incubator exists, keeping only dashboard version)
  createUserRoute('/dashboard/incubator', BusinessIncubator, 'Loading dashboard incubator...'),

  // ✅ MOVED: Mentorship routes moved to mentorRoutes.ts
  // ✅ MOVED: Investment routes moved to investorRoutes.ts

  // AI Features - VERIFIED ✅ (for regular users only, role-specific AI access in dedicated route files)
  createAIRoute('/chat/enhanced', ConsolidatedAIPage, 'Loading AI Assistant...', ['user', 'admin', 'super_admin']),
  createAIRoute('/chat/enhancedBusiness', ConsolidatedAIPage, 'Loading Enhanced Business Analysis...', ['user', 'admin', 'super_admin']),
  createAIRoute('/chat/enhancedBusiness/:businessIdeaId', ConsolidatedAIPage, 'Loading Business Analysis...', ['user', 'admin', 'super_admin']),

  // Settings - VERIFIED ✅
  createUserRoute('/settings', UserSettings, 'Loading settings...'),
];

export default userRoutes;
