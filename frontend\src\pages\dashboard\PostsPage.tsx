import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { Post, postsAPI } from '../../services/api';
import PostForm from '../../components/posts/forms/PostForm';
import EnhancedCRUDTable from '../../components/common/EnhancedCRUDTable';
// MainLayout removed - handled by routing system
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  FileText, 
  MessageSquare,
  Star,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  BookOpen,
  Tag,
  Image
} from 'lucide-react';

const PostsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);

  // CRUD operations for posts with enhanced features
  const postsCRUD = useCRUD({
    create: async (data: Partial<Post>) => {
      if (!user) throw new Error(t('auth.loginRequired'));
      return postsAPI.createPost({
        ...data,
        author_id: user.id
      });
    },
    read: () => postsAPI.getPosts(),
    update: (id: number, data: Partial<Post>) =>
      postsAPI.updatePost(id, data),
    delete: (id: number) => postsAPI.deletePost(id),
    search: async (query: string, filters?: Record<string, any>) => {
      // Implement search functionality
      const posts = await postsAPI.getPosts();
      return posts.filter(post =>
        post.title.toLowerCase().includes(query.toLowerCase()) ||
        post.content.toLowerCase().includes(query.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(query.toLowerCase())
      );
    }
  }, {
    enableOptimisticUpdates: true,
    enableCache: true,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes
    retryAttempts: 3,
    validation: (data: Partial<Post>) => {
      const errors: string[] = [];
      if (!data.title?.trim()) {
        errors.push(t('crud.validation.required', { field: t('posts.title') }));
      }
      if (!data.content?.trim()) {
        errors.push(t('crud.validation.required', { field: t('posts.content') }));
      }
      if (data.title && data.title.length < 5) {
        errors.push(t('crud.validation.minLength', { field: t('posts.title'), min: 5 }));
      }
      return errors.length > 0 ? errors : null;
    },
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedPost(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedPost(null);
      }
    },
    onError: (operation, error) => {
      console.error(`${operation} operation failed:`, error);
    }
  });

  // Load data on component mount
  useEffect(() => {
    postsCRUD.readItems();
  }, []);

  // Handle create
  const handleCreate = async (data: Partial<Post>) => {
    return await postsCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (post: Post) => {
    setSelectedPost(post);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<Post>) => {
    if (!selectedPost) return false;
    return await postsCRUD.updateItem(selectedPost.id, data);
  };

  // Handle delete
  const handleDelete = (post: Post) => {
    setSelectedPost(post);
    setShowDeleteDialog(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedPost) return;
    await postsCRUD.deleteItem(selectedPost.id);
  };

  // Get post type icon - handle missing post_type gracefully
  const getPostTypeIcon = (type?: string) => {
    switch (type) {
      case 'article':
        return <FileText size={16} className="text-blue-400" />;
      case 'discussion':
        return <MessageSquare size={16} className="text-green-400" />;
      case 'question':
        return <MessageSquare size={16} className="text-yellow-400" />;
      case 'announcement':
        return <FileText size={16} className="text-red-400" />;
      case 'tutorial':
        return <BookOpen size={16} className="text-purple-400" />;
      case 'news':
        return <FileText size={16} className="text-orange-400" />;
      default:
        return <FileText size={16} className="text-gray-400" />;
    }
  };

  // Enhanced table columns configuration
  const columns = [
    {
      key: 'title',
      label: t('posts.title', 'Title'),
      sortable: true,
      render: (value: any, post: Post) => (
        <div>
          <div className="flex items-center gap-2">
            {getPostTypeIcon((post as any).post_type)}
            <span className="font-medium text-white">{post.title}</span>
            {(post as any).is_featured && (
              <Star size={14} className="text-yellow-400" />
            )}
            {((post as any).featured_image || post.image) && (
              <Image size={14} className="text-blue-400" />
            )}
          </div>
          <div className="text-sm text-gray-400 truncate max-w-xs mt-1">
            {(post as any).excerpt || post.content?.substring(0, 100) + '...'}
          </div>
        </div>
      )
    },
    {
      key: 'post_type',
      label: t('posts.type', 'Type'),
      render: (post: Post) => {
        const postType = (post as any).post_type || 'article';
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            postType === 'article' ? 'bg-blue-900/50 text-blue-200' :
            postType === 'discussion' ? 'bg-green-900/50 text-green-200' :
            postType === 'question' ? 'bg-yellow-900/50 text-yellow-200' :
            postType === 'announcement' ? 'bg-red-900/50 text-red-200' :
            postType === 'tutorial' ? 'bg-purple-900/50 text-purple-200' :
            'bg-orange-900/50 text-orange-200'
          }`}>
            {t(`posts.types.${postType}`, postType)}
          </span>
        );
      }
    },
    {
      key: 'category',
      label: t('posts.category', 'Category'),
      render: (post: Post) => (
        <span className="text-gray-300">{(post as any).category || 'General'}</span>
      )
    },
    {
      key: 'author',
      label: t('posts.author', 'Author'),
      render: (post: Post) => (
        <div className="flex items-center gap-2">
          {post.author?.profile?.profile_image ? (
            <img 
              src={post.author.profile.profile_image} 
              alt={post.author.username}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-medium">
                {post.author?.first_name?.[0] || post.author?.username[0]}
              </span>
            </div>
          )}
          <span className="text-gray-300 text-sm">
            {post.author?.first_name} {post.author?.last_name}
          </span>
        </div>
      )
    },
    {
      key: 'engagement',
      label: t('posts.engagement', 'Engagement'),
      render: (post: Post) => {
        if (!post) return <span className="text-gray-400">-</span>;
        return (
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Eye size={14} className="text-blue-400" />
              <span className="text-gray-300 text-sm">{(post as any)?.views_count || 0}</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageSquare size={14} className="text-green-400" />
              <span className="text-gray-300 text-sm">{(post as any)?.comments_count || 0}</span>
            </div>
          </div>
        );
      }
    },
    {
      key: 'status',
      label: t('posts.status', 'Status'),
      render: (post: Post) => {
        if (!post) return <span className="text-gray-400">-</span>;
        const isPublished = (post as any).is_published !== undefined ? (post as any).is_published : true;
        return (
          <div className="flex items-center gap-2">
            {isPublished ? (
              <CheckCircle size={16} className="text-green-400" />
            ) : (
              <Clock size={16} className="text-yellow-400" />
            )}
            <span className={`text-sm ${
              isPublished ? 'text-green-400' : 'text-yellow-400'
            }`}>
              {isPublished ? t('posts.published', 'Published') : t('posts.draft', 'Draft')}
            </span>
          </div>
        );
      }
    },
    {
      key: 'created_at',
      label: t('common.created', 'Created'),
      render: (post: Post) => (
        <span className="text-gray-400 text-sm">
          {new Date(post.created_at).toLocaleDateString()}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('common.view', 'View'),
      icon: Eye,
      onClick: (post: Post) => {
        // Navigate to post detail page
        window.location.href = `/posts/${post.id}`;
      },
      variant: 'secondary' as const
    },
    {
      label: t('common.edit', 'Edit'),
      icon: Edit,
      onClick: handleEdit,
      variant: 'primary' as const,
      condition: (post: Post) => post.author?.id === user?.id
    },
    {
      label: t('common.delete', 'Delete'),
      icon: Trash2,
      onClick: handleDelete,
      variant: 'danger' as const,
      condition: (post: Post) => post.author?.id === user?.id
    }
  ];

  // Stats cards data
  const allPosts = postsCRUD.data || [];
  const userPosts = allPosts.filter(post => post && post.author?.id === user?.id);
  const publishedPosts = allPosts.filter(post => post && ((post as any).is_published !== undefined ? (post as any).is_published : true));
  const featuredPosts = allPosts.filter(post => post && ((post as any).is_featured || false));

  const stats = [
    {
      title: t('posts.totalPosts', 'Total Posts'),
      value: allPosts.length,
      icon: FileText,
      color: 'blue'
    },
    {
      title: t('posts.published', 'Published'),
      value: publishedPosts.length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('posts.featured', 'Featured'),
      value: featuredPosts.length,
      icon: Star,
      color: 'yellow'
    },
    {
      title: t('posts.myPosts', 'My Posts'),
      value: userPosts.length,
      icon: BookOpen,
      color: 'purple'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('posts.posts', 'Posts')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('posts.managePosts', 'Create and manage community posts')}
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="mt-4 sm:mt-0 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
          >
            <Plus size={20} />
            {t('posts.createPost', 'Create Post')}
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-6">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${
                  stat.color === 'blue' ? 'bg-blue-900/50' :
                  stat.color === 'green' ? 'bg-green-900/50' :
                  stat.color === 'yellow' ? 'bg-yellow-900/50' :
                  'bg-purple-900/50'
                }`}>
                  <stat.icon size={24} className={
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    stat.color === 'yellow' ? 'text-yellow-400' :
                    'text-purple-400'
                  } />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Error Display */}
        {postsCRUD.error && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-400" />
              <span className="text-red-200">{postsCRUD.error}</span>
            </div>
          </div>
        )}

        {/* Enhanced Posts Table */}
        <EnhancedCRUDTable
          data={postsCRUD.paginatedData}
          columns={columns}
          selectedItems={postsCRUD.selectedItems}
          onSelectionChange={postsCRUD.setSelectedItems}
          onView={(post) => window.location.href = `/posts/${post.id}`}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onCreate={() => setShowCreateForm(true)}
          isLoading={postsCRUD.isLoading}
          searchable={true}
          filterable={true}
          sortable={true}
          selectable={true}
          bulkActions={true}
          emptyMessage={t('posts.noPosts', 'No posts found. Create your first post to get started!')}
          title={t('posts.allPosts', 'All Posts')}
          createButtonLabel={t('posts.createPost', 'Create Post')}
          searchPlaceholder={t('posts.searchPosts', 'Search posts...')}
          className="bg-gray-800"
          pagination={postsCRUD.pagination}
          onPageChange={postsCRUD.goToPage}
          onPageSizeChange={postsCRUD.changePageSize}
          filters={postsCRUD.filters}
          onFiltersChange={(filters) => postsCRUD.setFilters(filters)}
          sortBy={postsCRUD.filters.sortBy}
          sortOrder={postsCRUD.filters.sortOrder}
          onSortChange={(sortBy, sortOrder) => postsCRUD.setFilters({ sortBy, sortOrder })}
        />
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <PostForm
          mode="create"
          onSubmit={handleCreate}
          onCancel={() => setShowCreateForm(false)}
          isSubmitting={postsCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedPost && (
        <PostForm
          mode="edit"
          initialData={selectedPost}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedPost(null);
          }}
          isSubmitting={postsCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedPost && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('posts.deleteConfirmation', 'Are you sure you want to delete this post? This will also delete all comments. This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedPost(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={postsCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={postsCRUD.isLoading}
                >
                  {postsCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
              </div>
        </div>
      </div>
  );
};

export default PostsPage;
