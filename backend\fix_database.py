#!/usr/bin/env python
"""
Fix database tables by creating missing tables
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.db import connection
from django.core.management import execute_from_command_line

def check_and_create_tables():
    cursor = connection.cursor()
    
    # Check if search_searchdocument table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='search_searchdocument'")
    search_table_exists = cursor.fetchone()
    
    # Check if token_blacklist_outstandingtoken table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='token_blacklist_outstandingtoken'")
    token_table_exists = cursor.fetchone()
    
    print(f"Search table exists: {bool(search_table_exists)}")
    print(f"Token blacklist table exists: {bool(token_table_exists)}")

    # Check if search_text column exists in search table
    if search_table_exists:
        cursor.execute("PRAGMA table_info(search_searchdocument)")
        columns = [column[1] for column in cursor.fetchall()]
        search_text_exists = 'search_text' in columns
        print(f"Search text column exists: {search_text_exists}")

        if not search_text_exists:
            print("Adding missing search_text column...")
            cursor.execute('ALTER TABLE search_searchdocument ADD COLUMN search_text text NULL')
            print("Search text column added successfully!")
    
    # Create search table if missing
    if not search_table_exists:
        print("Creating search_searchdocument table...")
        cursor.execute('''
            CREATE TABLE "search_searchdocument" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "content_type" varchar(20) NOT NULL,
                "object_id" integer NOT NULL,
                "title" varchar(255) NOT NULL,
                "content" text NOT NULL,
                "author" varchar(150) NOT NULL,
                "created_at" datetime NOT NULL,
                "updated_at" datetime NOT NULL,
                "tags" text NOT NULL,
                "metadata" text NOT NULL,
                "search_vector" text NULL,
                "search_text" text NULL
            )
        ''')
        
        # Create indexes
        cursor.execute('CREATE UNIQUE INDEX "search_searchdocument_content_type_object_id_a2af782a_uniq" ON "search_searchdocument" ("content_type", "object_id")')
        cursor.execute('CREATE INDEX "search_sear_content_b6dc6b_idx" ON "search_searchdocument" ("content_type")')
        cursor.execute('CREATE INDEX "search_sear_object__019ba5_idx" ON "search_searchdocument" ("object_id")')
        cursor.execute('CREATE INDEX "search_sear_created_9638b5_idx" ON "search_searchdocument" ("created_at")')
        print("Search table created successfully!")
    
    # Create token blacklist table if missing
    if not token_table_exists:
        print("Creating token_blacklist_outstandingtoken table...")
        cursor.execute('''
            CREATE TABLE "token_blacklist_outstandingtoken" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "token" text NOT NULL UNIQUE,
                "created_at" datetime NOT NULL,
                "expires_at" datetime NOT NULL,
                "user_id" integer NULL,
                FOREIGN KEY ("user_id") REFERENCES "auth_user" ("id")
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE "token_blacklist_blacklistedtoken" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "blacklisted_at" datetime NOT NULL,
                "token_id" integer NOT NULL UNIQUE,
                FOREIGN KEY ("token_id") REFERENCES "token_blacklist_outstandingtoken" ("id")
            )
        ''')
        print("Token blacklist tables created successfully!")
    
    # Mark migrations as applied
    cursor.execute("INSERT OR IGNORE INTO django_migrations (app, name, applied) VALUES ('search', '0001_initial', datetime('now'))")
    cursor.execute("INSERT OR IGNORE INTO django_migrations (app, name, applied) VALUES ('token_blacklist', '0001_initial', datetime('now'))")
    
    connection.commit()
    print("Database fix completed!")

if __name__ == '__main__':
    check_and_create_tables()
