import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { Post } from '../../../services/api';
import { Loader, Save, X, AlertCircle, FileText, Image, Video, Link } from 'lucide-react';
import { validateRequired, validateMinLength, validateMaxLength, validateUrl } from '../../../utils/localeValidation';

interface PostFormProps {
  initialData?: Partial<Post>;
  onSubmit: (data: Partial<Post>) => Promise<boolean>;
  onCancel: () => void;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  content: string;
  // Temporarily comment out new fields until migration is applied
  // excerpt: string;
  // post_type: 'article' | 'discussion' | 'question' | 'announcement' | 'tutorial' | 'news';
  // category: string;
  tags: string;
  // featured_image: string;
  // is_featured: boolean;
  // is_published: boolean;
  // allow_comments: boolean;
}

interface FormErrors {
  [key: string]: string;
}

const PostForm: React.FC<PostFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    content: initialData?.content || '',
    // excerpt: initialData?.excerpt || '',
    // post_type: initialData?.post_type || 'article',
    // category: initialData?.category || '',
    tags: '', // Convert tags array to string temporarily
    // featured_image: initialData?.featured_image || '',
    // is_featured: initialData?.is_featured ?? false,
    // is_published: initialData?.is_published ?? true,
    // allow_comments: initialData?.allow_comments ?? true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation rules
  const validateField = (name: string, value: string | boolean): string => {
    switch (name) {
      case 'title':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && !validateMinLength(value, 5)) {
          return t('validation.minLength', 'Must be at least 5 characters');
        }
        if (typeof value === 'string' && !validateMaxLength(value, 200)) {
          return t('validation.maxLength', 'Must be less than 200 characters');
        }
        break;
      case 'content':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && !validateMinLength(value, 50)) {
          return t('validation.minLength', 'Must be at least 50 characters');
        }
        break;
      case 'excerpt':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        if (typeof value === 'string' && !validateMinLength(value, 20)) {
          return t('validation.minLength', 'Must be at least 20 characters');
        }
        if (typeof value === 'string' && !validateMaxLength(value, 300)) {
          return t('validation.maxLength', 'Must be less than 300 characters');
        }
        break;
      case 'category':
        if (!validateRequired(value)) {
          return t('validation.required', 'This field is required');
        }
        break;
      case 'featured_image':
        if (value && typeof value === 'string' && value.trim()) {
          if (!validateUrl(value)) {
            return t('validation.invalidUrl', 'Must be a valid URL starting with http:// or https://');
          }
        }
        break;
    }
    return '';
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    Object.keys(formData).forEach(key => {
      if (key !== 'is_featured' && key !== 'is_published' && key !== 'allow_comments' && key !== 'tags' && key !== 'featured_image') {
        const error = validateField(key, formData[key as keyof FormData]);
        if (error) {
          newErrors[key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: string | boolean = value;
    
    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    let processedValue: string | boolean = value;
    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    const error = validateField(name, processedValue);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Submit form
    const success = await onSubmit(formData);
    if (success) {
      // Form will be closed by parent component
    }
  };

  const postTypeOptions = [
    { value: 'article', label: t('posts.types.article', 'Article'), icon: FileText },
    { value: 'discussion', label: t('posts.types.discussion', 'Discussion'), icon: FileText },
    { value: 'question', label: t('posts.types.question', 'Question'), icon: FileText },
    { value: 'announcement', label: t('posts.types.announcement', 'Announcement'), icon: FileText },
    { value: 'tutorial', label: t('posts.types.tutorial', 'Tutorial'), icon: FileText },
    { value: 'news', label: t('posts.types.news', 'News'), icon: FileText }
  ];

  const categoryOptions = [
    'General',
    'Business',
    'Technology',
    'Marketing',
    'Finance',
    'Legal',
    'Operations',
    'Product Development',
    'Strategy',
    'Networking'
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-semibold text-white">
            {mode === 'create' 
              ? t('posts.createPost', 'Create Post')
              : t('posts.editPost', 'Edit Post')
            }
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('posts.title', 'Title')} *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white ${
                errors.title && touched.title ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('posts.titlePlaceholder', 'Enter post title')}
              disabled={isSubmitting}
            />
            {errors.title && touched.title && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('posts.content', 'Content')} *
            </label>
            <textarea
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              onBlur={handleBlur}
              rows={8}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-vertical ${
                errors.content && touched.content ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={t('posts.contentPlaceholder', 'Write your post content here...')}
              disabled={isSubmitting}
            />
            {errors.content && touched.content && (
              <p className="mt-1 text-sm text-red-400 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {errors.content}
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className={`flex gap-4 pt-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
              disabled={isSubmitting}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader className="animate-spin mr-2" size={16} />
                  {mode === 'create' ? t('common.creating', 'Creating...') : t('common.updating', 'Updating...')}
                </>
              ) : (
                <>
                  <Save className="mr-2" size={16} />
                  {mode === 'create' ? t('posts.createPost', 'Create') : t('posts.updatePost', 'Update')}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PostForm;
