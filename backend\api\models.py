from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
from .storage import OptimizedImageStorage

class Tag(models.Model):
    """Tags for categorizing content"""
    name = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class Event(models.Model):
    MODERATION_STATUS = (
        ('approved', 'Approved'),
        ('pending', 'Pending Review'),
        ('rejected', 'Rejected'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField()
    date = models.DateTimeField()
    location = models.Char<PERSON>ield(max_length=200)
    is_virtual = models.BooleanField(default=False)
    virtual_link = models.URLField(blank=True, null=True)
    image = models.ImageField(upload_to='event_images/', storage=OptimizedImageStorage(), blank=True, null=True)
    organizer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='organized_events')
    attendees = models.ManyToManyField(User, related_name='attending_events', blank=True)
    moderation_status = models.CharField(max_length=20, choices=MODERATION_STATUS, default='pending')
    moderation_comment = models.TextField(blank=True, null=True)
    moderated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='moderated_events')
    moderated_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Resource(models.Model):
    RESOURCE_TYPES = (
        ('article', 'Article'),
        ('video', 'Video'),
        ('course', 'Course'),
        ('book', 'Book'),
        ('tool', 'Tool'),
        ('other', 'Other'),
    )

    title = models.CharField(max_length=200)
    description = models.TextField()
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)
    url = models.URLField()
    image = models.ImageField(upload_to='resource_images/', storage=OptimizedImageStorage(), blank=True, null=True)
    author = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='shared_resources')
    tags = models.ManyToManyField(Tag, related_name='resources', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Post(models.Model):
    MODERATION_STATUS = (
        ('approved', 'Approved'),
        ('pending', 'Pending Review'),
        ('rejected', 'Rejected'),
    )

    POST_TYPES = (
        ('article', 'Article'),
        ('discussion', 'Discussion'),
        ('question', 'Question'),
        ('announcement', 'Announcement'),
        ('tutorial', 'Tutorial'),
        ('news', 'News'),
    )

    CATEGORIES = (
        ('general', 'General'),
        ('business', 'Business'),
        ('technology', 'Technology'),
        ('marketing', 'Marketing'),
        ('finance', 'Finance'),
        ('legal', 'Legal'),
        ('operations', 'Operations'),
        ('product_development', 'Product Development'),
        ('strategy', 'Strategy'),
        ('networking', 'Networking'),
    )

    title = models.CharField(max_length=200)
    excerpt = models.TextField(max_length=500, help_text="Brief summary of the post")
    content = models.TextField()
    post_type = models.CharField(max_length=20, choices=POST_TYPES, default='article')
    category = models.CharField(max_length=30, choices=CATEGORIES, default='general')
    author = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='posts')
    image = models.ImageField(upload_to='post_images/', storage=OptimizedImageStorage(), blank=True, null=True)
    featured_image = models.URLField(blank=True, null=True, help_text="URL to featured image")
    is_featured = models.BooleanField(default=False, help_text="Featured posts appear prominently")
    is_published = models.BooleanField(default=True, help_text="Only published posts are visible to users")
    allow_comments = models.BooleanField(default=True, help_text="Allow users to comment on this post")
    likes = models.ManyToManyField(User, related_name='liked_posts', blank=True)
    tags = models.ManyToManyField(Tag, related_name='posts', blank=True)
    moderation_status = models.CharField(max_length=20, choices=MODERATION_STATUS, default='pending')
    moderation_comment = models.TextField(blank=True, null=True)
    moderated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='moderated_posts')
    moderated_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    @property
    def like_count(self):
        return self.likes.count()

class Comment(models.Model):
    MODERATION_STATUS = (
        ('approved', 'Approved'),
        ('pending', 'Pending Review'),
        ('rejected', 'Rejected'),
    )

    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='comments')
    content = models.TextField()
    moderation_status = models.CharField(max_length=20, choices=MODERATION_STATUS, default='pending')
    moderation_comment = models.TextField(blank=True, null=True)
    moderated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='moderated_comments')
    moderated_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Comment by {self.author.username} on {self.post.title}"

class MembershipApplication(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    )

    EXPERTISE_LEVEL_CHOICES = (
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='membership_applications', null=True, blank=True)
    full_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True)
    location = models.CharField(max_length=100)
    expertise_areas = models.TextField(help_text="Areas of expertise in AI/Data Science")
    expertise_level = models.CharField(max_length=20, choices=EXPERTISE_LEVEL_CHOICES)
    background = models.TextField(help_text="Educational and professional background")
    motivation = models.TextField(help_text="Motivation for joining the community")
    linkedin_profile = models.URLField(blank=True, null=True)
    github_profile = models.URLField(blank=True, null=True)
    portfolio_url = models.URLField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_applications')
    reviewed_at = models.DateTimeField(null=True, blank=True)
    review_notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Membership application by {self.full_name}"

    class Meta:
        ordering = ['-created_at']


# Import SystemLog model
from .models_logs import SystemLog
