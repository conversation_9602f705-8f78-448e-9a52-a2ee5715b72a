/**
 * Template Viewer Component
 * Comprehensive template viewing and preview functionality
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Star,
  Clock,
  Users,
  CheckCircle,
  Play,
  Share2,
  Bookmark,
  Maximize2,
  X,
  Edit
} from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';

interface TemplateSection {
  id: string;
  title: string;
  description: string;
  content: string;
  order: number;
  required: boolean;
  estimated_time: number;
  ai_prompt?: string;
  guiding_questions?: string[];
}

interface TemplateData {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  sections: TemplateSection[];
  popularity: number;
  rating: number;
  totalRatings: number;
  usageCount: number;
  completionRate: number;
  isNew?: boolean;
  isPremium?: boolean;
  tags: string[];
  author: string;
  lastUpdated: string;
  preview: {
    executive_summary: string;
    market_analysis: string;
    financial_projections: string;
  };
}

interface TemplateViewerProps {
  templateId: string;
  isOpen: boolean;
  onClose: () => void;
  onUseTemplate?: (templateId: string) => void;
  onEditTemplate?: (templateId: string) => void;
  mode?: 'preview' | 'full' | 'edit';
}

const TemplateViewer: React.FC<TemplateViewerProps> = ({ templateId,
  isOpen,
  onClose,
  onUseTemplate,
  onEditTemplate,
  mode = 'preview'
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [template, setTemplate] = useState<TemplateData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string>('overview');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);

  useEffect(() => {
    if (isOpen && templateId) {
      loadTemplateData();
    }
  }, [isOpen, templateId]);

  const loadTemplateData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Convert string templateId to number if needed
      const numericId = typeof templateId === 'string' ? parseInt(templateId) : templateId;

      // Import the API function
      const { businessPlanTemplatesAPI } = await import('../../services/templateCustomizationApi');
      const templateData = await businessPlanTemplatesAPI.getTemplate(numericId);

      // Transform API data to match component interface
      const transformedTemplate: TemplateData = {
        id: templateData.id.toString(),
        name: templateData.name,
        description: templateData.description,
        category: templateData.industry?.toLowerCase().replace(/\s+/g, '_') || 'general',
        difficulty: templateData.difficulty_level || 'intermediate',
        estimatedTime: templateData.estimated_time || 8,
        popularity: templateData.popularity_score || 0, // Use real popularity score from API
        rating: templateData.rating || 0,
        totalRatings: templateData.rating_count || 0,
        usageCount: templateData.usage_count || 0,
        completionRate: templateData.completion_rate || 0, // Use real data from API
        isNew: templateData.is_new || false, // Use real is_new from API
        isPremium: templateData.is_premium || false, // Use real premium status from API
        tags: templateData.tags || [],
        author: templateData.author_details?.username || templateData.author_details?.first_name || t("common.system", "System"),
        lastUpdated: templateData.updated_at || templateData.created_at || new Date().toISOString(),
        sections: (() => {
          // Handle both old and new data structures
          const sectionsData = templateData.sections?.sections || templateData.sections || {};

          // If it's already an array, use it directly
          if (Array.isArray(sectionsData)) {
            return sectionsData.map((section: any, index: number) => ({
              id: section.key || section.id || `section_${index}`,
              title: section.title || 'Untitled Section',
              description: section.description || '',
              content: section.content || '',
              order: section.order || index,
              required: section.is_required || section.required || false,
              estimated_time: section.estimated_time || 30,
              ai_prompt: section.ai_prompt,
              guiding_questions: section.guiding_questions || []
            }));
          }

          // If it's an object, convert to array
          return Object.entries(sectionsData).map(([key, section]: [string, any]) => ({
            id: key,
            title: section.title || key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
            description: section.description || '',
            content: section.content || '',
            order: section.order || 0,
            required: section.required || false,
            estimated_time: section.estimated_time || 30,
            ai_prompt: section.ai_prompt,
            guiding_questions: section.guiding_questions || []
          }));
        })(),
        preview: {
          executive_summary: templateData.sections?.executive_summary?.content || templateData.sections?.sections?.executive_summary?.content || '',
          market_analysis: templateData.sections?.market_analysis?.content || templateData.sections?.sections?.market_analysis?.content || '',
          financial_projections: templateData.sections?.financial_projections?.content || templateData.sections?.sections?.financial_projections?.content || ''
        }
      };

      setTemplate(transformedTemplate);
    } catch (err) {
      console.error('Error loading template:', err);
      setError(t("common.failed.to.load", "Failed to load template data"));
    } finally {
      setLoading(false);
    }
  };





  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400 bg-green-400/10';
      case 'intermediate': return 'text-yellow-400 bg-yellow-400/10';
      case 'advanced': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  const handleUseTemplate = () => {
    if (onUseTemplate && template) {
      onUseTemplate(template.id);
    }
  };

  const handleEditTemplate = () => {
    if (onEditTemplate && template) {
      onEditTemplate(template.id);
    }
  };

  if (!isOpen) return null;

  if (loading) {
    return (
      <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="bg-gray-800 rounded-lg p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
          <p className="text-white mt-4">{t("common.loading.template", "Loading template...")}</p>
        </div>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="bg-gray-800 rounded-lg p-8 max-w-md">
          <div className="text-center">
            <FileText size={48} className="text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">{t("common.template.not.found", "Template Not Found")}</h3>
            <p className="text-gray-400 mb-4">
              {error || t("common.the.requested.template", "The requested template could not be loaded.")}
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${isFullscreen ? 'p-0' : 'p-4'}`}>
      <div className={`bg-gray-900 rounded-lg shadow-2xl ${isFullscreen ? 'w-full h-full' : 'max-w-6xl w-full max-h-[90vh]'} flex flex-col`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="p-2 bg-purple-600/20 rounded-lg">
              <FileText size={24} className="text-purple-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{template.name}</h2>
              <div className={`flex items-center space-x-4 mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(template.difficulty)}`}>
                  {template.difficulty}
                </span>
                <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  {renderStars(template.rating)}
                  <span className={`text-sm text-gray-400 ml-1 ${isRTL ? "space-x-reverse" : ""}`}>
                    ({template.totalRatings})
                  </span>
                </div>
                <div className={`flex items-center text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  {template.estimatedTime}h
                </div>
                <div className={`flex items-center text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Users size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  {template.usageCount.toLocaleString()} uses
                </div>
              </div>
            </div>
          </div>

          <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setIsBookmarked(!isBookmarked)}
              className={`p-2 rounded-lg transition-colors ${
                isBookmarked ? 'bg-yellow-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
              }`}
            >
              <Bookmark size={16} />
            </button>

            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-gray-300 transition-colors"
            >
              <Maximize2 size={16} />
            </button>

            <button
              onClick={onClose}
              className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-gray-300 transition-colors"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className={`flex flex-1 overflow-hidden ${isRTL ? "flex-row-reverse" : ""}`}>
          {/* Sidebar Navigation */}
          <div className="w-64 bg-gray-800/50 border-r border-gray-700 p-4 overflow-y-auto">
            <div className="space-y-2">
              <button
                onClick={() => setActiveSection('overview')}
                className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                  activeSection === 'overview'
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700'}
                }`}
              >
                Overview
              </button>

              {template.sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    activeSection === section.id
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700'}
                  }`}
                >
                  <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className="text-sm">{section.title}</span>
                    {section.required && (
                      <CheckCircle size={12} className="text-green-400" />
                    )}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {section.estimated_time}min
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className={`flex-1 p-6 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
            {activeSection === 'overview' ? (
              <div className="space-y-6">
                {/* Template Info */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">{t("common.about.this.template", "About This Template")}</h3>
                  <p className="text-gray-300 leading-relaxed mb-4">
                    {template.description}
                  </p>

                  <div className={`flex flex-wrap gap-2 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {template.tags.map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-purple-600/20 text-purple-400 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Template Sections Overview */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">{t("common.template.sections", "Template Sections")}</h3>
                  <div className="space-y-3">
                    {template.sections.map((section, index) => (
                      <div key={section.id} className="bg-gray-800/50 rounded-lg p-4">
                        <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <h4 className="font-medium text-white">
                            {index + 1}. {section.title}
                          </h4>
                          <div className={`flex items-center space-x-2 text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <Clock size={14} />
                            <span>{section.estimated_time}min</span>
                            {section.required && (
                              <CheckCircle size={14} className="text-green-400" />
                            )}
                          </div>
                        </div>
                        <p className="text-gray-300 text-sm leading-relaxed">
                          {section.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Template Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gray-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-400">{template.sections.length}</div>
                    <div className="text-sm text-gray-400">{t("common.sections", "Sections")}</div>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-400">{template.completionRate}%</div>
                    <div className="text-sm text-gray-400">{t("common.completion.rate", "Completion Rate")}</div>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-400">{template.estimatedTime}h</div>
                    <div className="text-sm text-gray-400">{t("common.est.time", "Est. Time")}</div>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-400">{template.rating}/5</div>
                    <div className="text-sm text-gray-400">{t("common.rating", "Rating")}</div>
                  </div>
                </div>
              </div>
            ) : (
              // Section Content
              (() => {
                const section = template.sections.find(s => s.id === activeSection);
                if (!section) return null;

                return (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">{section.title}</h3>
                      <p className="text-gray-400 mb-4">{section.description}</p>

                      <div className={`flex items-center space-x-4 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <div className={`flex items-center text-sm text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <Clock size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                          {section.estimated_time} minutes
                        </div>
                        {section.required && (
                          <div className={`flex items-center text-sm text-green-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <CheckCircle size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                            Required
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-800/50 rounded-lg p-6">
                      <h4 className="font-medium text-white mb-3">{t("common.section.content.preview", "Section Content Preview")}</h4>
                      <p className="text-gray-300 leading-relaxed mb-4">
                        {section.content}
                      </p>
                    </div>

                    {section.guiding_questions && (
                      <div className="bg-gray-800/50 rounded-lg p-6">
                        <h4 className="font-medium text-white mb-3">{t("common.guiding.questions", "Guiding Questions")}</h4>
                        <ul className="space-y-2">
                          {section.guiding_questions.map((question, index) => (
                            <li key={index} className={`flex items-start text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
                              <span className={`text-purple-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`}>•</span>
                              {question}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {section.ai_prompt && (
                      <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-6">
                        <h4 className="font-medium text-purple-400 mb-3">{t("common.ai.assistance.available", "AI Assistance Available")}</h4>
                        <p className="text-gray-300 text-sm">
                          Our AI can help you generate content for this section using the prompt: "{section.ai_prompt}"
                        </p>
                      </div>
                    )}
                  </div>
                );
              })()
            )}
          </div>
        </div>

        {/* Footer Actions */}
        <div className={`flex items-center justify-between p-6 border-t border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <span className="text-sm text-gray-400">
              Last updated: {new Date(template.lastUpdated).toLocaleDateString()}
            </span>
            <span className="text-sm text-gray-400">
              by {template.author}
            </span>
          </div>

          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => navigator.clipboard.writeText(window.location.href)}
              className={`px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Share2 size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              Share
            </button>

            {onEditTemplate && (
              <button
                onClick={handleEditTemplate}
                className={`px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Edit size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                Edit
              </button>
            )}

            <button
              onClick={handleUseTemplate}
              className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Play size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              Use This Template
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateViewer;
