/**
 * Collaborative Templates Page
 * Manage and work on collaborative business plan templates
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Users,
  Plus,
  Edit3,
  Share2,
  MessageCircle,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  UserPlus
} from 'lucide-react';
// MainLayout removed - handled by routing system
import CollaborativeTemplateEditor from '../../components/collaboration/CollaborativeTemplateEditor';
import { RTLText, RTLFlex } from '../../components/common';

interface CollaborativeTemplate {
  id: string;
  name: string;
  description: string;
  collaborators: number;
  lastActivity: string;
  status: 'active' | 'completed' | 'draft';
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  unreadComments: number;
  progress: number;
}

const CollaborativeTemplatesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const navigate = useNavigate();
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [showEditor, setShowEditor] = useState(false);

  // Load collaborative templates from API
  const [collaborativeTemplates, setCollaborativeTemplates] = useState<CollaborativeTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCollaborativeTemplates = async () => {
      try {
        // Load collaborative templates from API
        // For now, we'll show demo data to demonstrate the interface
        const demoTemplates: CollaborativeTemplate[] = [
          {
            id: '1',
            name: 'Tech Startup Business Plan',
            description: 'Comprehensive business plan for technology startup seeking Series A funding',
            collaborators: 4,
            lastActivity: '2024-01-15T14:30:00Z',
            status: 'active',
            role: 'owner',
            unreadComments: 3,
            progress: 75
          },
          {
            id: '2',
            name: 'E-commerce Platform Strategy',
            description: 'Strategic business plan for online marketplace expansion',
            collaborators: 2,
            lastActivity: '2024-01-15T12:15:00Z',
            status: 'draft',
            role: 'editor',
            unreadComments: 1,
            progress: 45
          },
          {
            id: '3',
            name: 'SaaS Product Launch Plan',
            description: 'Go-to-market strategy for B2B SaaS product launch',
            collaborators: 6,
            lastActivity: '2024-01-14T16:45:00Z',
            status: 'completed',
            role: 'reviewer',
            unreadComments: 0,
            progress: 100
          }
        ];
        setCollaborativeTemplates(demoTemplates);
      } catch (err) {
        console.error('Error loading collaborative templates:', err);
        setError('Failed to load collaborative templates. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadCollaborativeTemplates();
  }, []);

  const currentUser = {
    id: '1',
    name: t("dashboard.ahmed.hassan", "Ahmed Hassan"),
    email: '<EMAIL>',
    role: 'owner' as const,
    isOnline: true,
    lastSeen: '2024-01-15T10:30:00Z',
    permissions: { canEdit: true, canComment: true, canInvite: true, canApprove: true }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-600/20';
      case 'completed': return 'text-blue-400 bg-blue-600/20';
      case 'draft': return 'text-yellow-400 bg-yellow-600/20';
      default: return 'text-gray-400 bg-gray-600/20';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'text-purple-400 bg-purple-600/20';
      case 'editor': return 'text-blue-400 bg-blue-600/20';
      case 'reviewer': return 'text-green-400 bg-green-600/20';
      case 'viewer': return 'text-gray-400 bg-gray-600/20';
      default: return 'text-gray-400 bg-gray-600/20';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return t('time.justNow');
    if (diffInMinutes < 60) return t('time.minutesAgo', { count: diffInMinutes });
    if (diffInMinutes < 1440) return t('time.hoursAgo', { count: Math.floor(diffInMinutes / 60) });
    return t('time.daysAgo', { count: Math.floor(diffInMinutes / 1440) });
  };

  const handleTemplateClick = (templateId: string) => {
    setSelectedTemplate(templateId);
    setShowEditor(true);
  };

  const handleCreateNew = () => {
    navigate('/dashboard/templates/custom');
  };

  const handleInviteCollaborator = (email: string, role: string) => {
    console.log("Inviting collaborator:", email, role);
    // Implementation would send invitation
  };

  if (showEditor && selectedTemplate) {
    return (
      <CollaborativeTemplateEditor
        templateId={selectedTemplate}
        currentUser={currentUser}
        onSave={(data) => console.log("Saving:", data)}
        onInviteCollaborator={handleInviteCollaborator}
      />
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
        {/* Page Header */}
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLFlex className="items-center justify-between">
            <div>
              <RTLFlex className="items-center mb-2">
                <Users className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={28} />
                <RTLText as="h1" className="text-2xl font-bold">
                  {t('collaboration.collaborativeTemplates')}
                </RTLText>
              </RTLFlex>
              <p className="text-gray-300">
                {t('collaboration.collaborativeTemplatesDesc')}
              </p>
            </div>

            <button
              onClick={handleCreateNew}
              className={`px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('collaboration.createNew')}
            </button>
          </RTLFlex>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {collaborativeTemplates.map((template) => (
            <div
              key={template.id}
              className="bg-gray-800/50 rounded-lg p-6 hover:bg-gray-700/50 transition-colors cursor-pointer"
              onClick={() => handleTemplateClick(template.id)}
            >
              {/* Template Header */}
              <RTLFlex className="items-start justify-between mb-4">
                <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <RTLText as="h3" className="text-lg font-semibold mb-2">
                    {template.name}
                  </RTLText>
                  <p className="text-gray-400 text-sm mb-3">
                    {template.description}
                  </p>
                </div>

                {template.unreadComments > 0 && (
                  <div className={`bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    {template.unreadComments}
                  </div>
                )}
              </RTLFlex>

              {/* Progress Bar */}
              <div className="mb-4">
                <RTLFlex className="items-center justify-between mb-2">
                  <span className="text-sm text-gray-400">{t('common.progress')}</span>
                  <span className="text-sm font-medium">{template.progress}%</span>
                </RTLFlex>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${template.progress}%` }}
                  />
                </div>
              </div>

              {/* Template Info */}
              <div className="space-y-3">
                {/* Status and Role */}
                <RTLFlex className="items-center justify-between">
                  <span className={`px-2 py-1 rounded text-xs ${getStatusColor(template.status)}`}>
                    {t(`collaboration.status.${template.status}`)}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs ${getRoleColor(template.role)}`}>
                    {t(`roles.${template.role}`)}
                  </span>
                </RTLFlex>

                {/* Collaborators and Activity */}
                <RTLFlex className="items-center justify-between text-sm text-gray-400">
                  <RTLFlex className="items-center">
                    <Users size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {template.collaborators} {t('collaboration.collaborators')}
                  </RTLFlex>
                  <RTLFlex className="items-center">
                    <Clock size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    {formatTimeAgo(template.lastActivity)}
                  </RTLFlex>
                </RTLFlex>

                {/* Action Buttons */}
                <RTLFlex className="items-center justify-between pt-3 border-t border-gray-700">
                  <RTLFlex className="items-center space-x-3">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTemplateClick(template.id);
                      }}
                      className="text-purple-400 hover:text-purple-300 transition-colors"
                      title={t('collaboration.edit')}
                    >
                      <Edit3 size={16} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle share
                      }}
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                      title={t('collaboration.share')}
                    >
                      <Share2 size={16} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle comments
                      }}
                      className="text-green-400 hover:text-green-300 transition-colors relative"
                      title={t('collaboration.comments')}
                    >
                      <MessageCircle size={16} />
                      {template.unreadComments > 0 && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full" />
                      )}
                    </button>
                  </RTLFlex>

                  {template.role === 'owner' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle invite
                      }}
                      className="text-yellow-400 hover:text-yellow-300 transition-colors"
                      title={t('collaboration.invite')}
                    >
                      <UserPlus size={16} />
                    </button>
                  )}
                </RTLFlex>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {collaborativeTemplates.length === 0 && (
          <div className="text-center py-12">
            <Users size={48} className="text-gray-600 mx-auto mb-4" />
            <RTLText as="h3" className="text-xl font-semibold text-gray-400 mb-2">
              {t('collaboration.noTemplates')}
            </RTLText>
            <p className="text-gray-500 mb-6">
              {t('collaboration.noTemplatesDesc')}
            </p>
            <button
              onClick={handleCreateNew}
              className={`px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg text-white transition-colors flex items-center mx-auto ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Plus size={20} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('collaboration.createFirst')}
            </button>
          </div>
        )}
      </div>
              </div>
        </div>
      </div>
    </div>
  );
};

export default CollaborativeTemplatesPage;
