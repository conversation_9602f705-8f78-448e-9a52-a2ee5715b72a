import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import TemplateSelector from '../../components/templates/TemplateSelector';
import TemplateViewer from '../../components/templates/TemplateViewer';
import { TemplateMarketplace } from '../../components/incubator/TemplateMarketplace';
import { RTLText, RTLFlex } from '../../components/common';
import {
  BookOpen,
  Store,
  Brain,
  ArrowLeft,
  Sparkles,
  Search,
  FileText
} from 'lucide-react';

const TemplatesOverviewPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [activeSection, setActiveSection] = useState<'landing' | 'browse' | 'marketplace' | 'ai' | 'template-selector'>('landing');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [previewTemplateId, setPreviewTemplateId] = useState<string>('');
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const handleSelectTemplate = (template: any) => {
    if (typeof template === 'string') {
      setSelectedTemplate(template);
    } else {
      navigate(`/dashboard/business-plans/new?template=${template.id}`);
    }
  };

  const handlePreviewTemplate = (template: any) => {
    if (typeof template === 'string') {
      setPreviewTemplateId(template);
      setShowPreviewModal(true);
    } else {
      console.log("Previewing template:", template);
    }
  };



  const renderTemplateSelector = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          onClick={() => setActiveSection('landing')}
          className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
          {t('common.back')}
        </button>
        <RTLText as="h2" className="text-2xl font-bold text-white">
          {t('templates.browseTemplates', 'Browse Templates')}
        </RTLText>
      </div>

      <TemplateSelector
        onSelectTemplate={handleSelectTemplate}
        selectedTemplate={selectedTemplate}
        showCategories={true}
        showFilters={true}
        showActionButtons={true}
        onPreviewTemplate={handlePreviewTemplate}
        onUseTemplate={(templateId) => {
          navigate(`/dashboard/business-plans/new?template=${templateId}`);
        }}
        onCreateTemplate={() => {
          navigate('/dashboard/templates/create');
        }}
      />

      {selectedTemplate && (
        <div className="bg-black/30 backdrop-blur-sm border border-green-500/50 rounded-lg p-4">
          <p className="text-green-400">
            ✅ Template selected: {selectedTemplate}
          </p>
          <p className="text-gray-300 text-sm mt-2">
            Click "Preview" to see template details or "Use Template" to start creating a business plan.
          </p>
        </div>
      )}
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'landing':
        return (
          <div className="space-y-8">
            {/* Welcome Section */}
            <div className="text-center">
              <h1 className="text-4xl font-bold text-white mb-4">
                {t('templates.welcome', 'Welcome to Templates')}
              </h1>
              <p className="text-gray-300 text-lg">
                {t('templates.welcomeDescription', 'Choose from our collection of professional business plan templates')}
              </p>
            </div>

            {/* Quick Access Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div
                onClick={() => setActiveSection('browse')}
                className="group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-blue-400/50 cursor-pointer transition-all duration-300"
              >
                <BookOpen className="w-8 h-8 text-blue-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <RTLText as="h3" className="text-lg font-semibold mb-2 text-white">
                  {t('templates.browseTemplates', 'Browse Templates')}
                </RTLText>
                <RTLText className="text-sm text-gray-300">
                  {t('templates.browseDescription', 'Explore our collection of business plan templates')}
                </RTLText>
              </div>

              <div
                onClick={() => setActiveSection('marketplace')}
                className="group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-green-400/50 cursor-pointer transition-all duration-300"
              >
                <Store className="w-8 h-8 text-green-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <RTLText as="h3" className="text-lg font-semibold mb-2 text-white">
                  {t('marketplace.templateMarketplace', 'Template Marketplace')}
                </RTLText>
                <RTLText className="text-sm text-gray-300">
                  {t('marketplace.description', 'Discover premium templates from our marketplace')}
                </RTLText>
              </div>

              <div
                onClick={() => setActiveSection('ai')}
                className="group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-purple-400/50 cursor-pointer transition-all duration-300"
              >
                <Brain className="w-8 h-8 text-purple-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <RTLText as="h3" className="text-lg font-semibold mb-2 text-white">
                  {t('ai.smartTemplateRecommendations', 'AI Recommendations')}
                </RTLText>
                <RTLText className="text-sm text-gray-300">
                  {t('ai.recommendationsDescription', 'Get AI-powered template recommendations')}
                </RTLText>
              </div>
            </div>
          </div>
        );

      case 'browse':
      case 'template-selector':
        return renderTemplateSelector();

      case 'marketplace':
        return (
          <div className="space-y-6">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setActiveSection('landing')}
                className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                {t('common.back')}
              </button>
              <RTLText as="h2" className="text-2xl font-bold text-white">
                {t('marketplace.templateMarketplace', 'Template Marketplace')}
              </RTLText>
            </div>

            {/* Template Marketplace */}
            <TemplateMarketplace
              onPurchaseTemplate={(template) => {
                // Handle template purchase
                console.log('Purchase template:', template);
              }}
              onPreviewTemplate={(template) => {
                setPreviewTemplateId(template.id.toString());
                setShowPreviewModal(true);
              }}
            />


          </div>
        );

      case 'ai':
        return (
          <div className="space-y-6">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setActiveSection('landing')}
                className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                {t('common.back')}
              </button>
              <RTLText as="h2" className="text-2xl font-bold text-white">
                {t('ai.smartTemplateRecommendations', 'AI Template Recommendations')}
              </RTLText>
            </div>

            {/* AI Recommendations Content */}
            <div className="space-y-6">
              {/* AI Generator Access */}
              <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-8 text-center">
                <div className="mb-6">
                  <Brain className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {t('ai.templateGenerator', 'AI Template Generator')}
                  </h3>
                  <p className="text-gray-300 text-lg">
                    {t('ai.generatorDescription', 'Use AI to create personalized business plan templates based on your specific needs and industry')}
                  </p>
                </div>
                <button
                  onClick={() => navigate('/dashboard/templates/ai-generator')}
                  className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors flex items-center justify-center mx-auto"
                >
                  <Sparkles size={20} className="mr-2" />
                  {t('ai.startGenerator', 'Start AI Generator')}
                </button>
              </div>

              {/* Template Recommendation Engine */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
                <h3 className="text-xl font-bold text-white mb-4">
                  {t('ai.recommendationEngine', 'Smart Template Recommendations')}
                </h3>
                <p className="text-gray-300 mb-6">
                  {t('ai.recommendationDescription', 'Get personalized template recommendations based on your business ideas and preferences')}
                </p>
                <button
                  onClick={() => navigate('/dashboard/templates/ai-generator')}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors flex items-center"
                >
                  <Brain size={20} className="mr-2" />
                  {t('ai.getRecommendations', 'Get AI Recommendations')}
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
          {/* Navigation Pills (only show when not on landing) */}
          {activeSection !== 'landing' && (
            <div className={`flex flex-wrap gap-2 p-4 bg-gray-800/50 rounded-lg border border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setActiveSection('landing')}
                className={`px-4 py-2 bg-purple-600 text-white rounded-lg font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Sparkles size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('templates.home', 'Home')}
              </button>

              <button
                onClick={() => setActiveSection('browse')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  activeSection === 'browse' || activeSection === 'template-selector'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <BookOpen size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('templates.browseTemplates', 'Browse Templates')}
              </button>

              <button
                onClick={() => setActiveSection('marketplace')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  activeSection === 'marketplace'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <Store size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('marketplace.templateMarketplace', 'Marketplace')}
              </button>

              <button
                onClick={() => setActiveSection('ai')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  activeSection === 'ai'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <Brain size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('ai.smartTemplateRecommendations', 'AI Recommendations')}
              </button>
            </div>
          )}

          {/* Main Content */}
          <div className="min-h-[600px]">
            {renderSectionContent()}
          </div>
        </div>

        {/* Template Viewer Modal */}
        <TemplateViewer
          templateId={previewTemplateId}
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          onUseTemplate={(templateId) => {
            navigate(`/dashboard/business-plans/new?template=${templateId}`);
            setShowPreviewModal(false);
          }}
          mode="preview"
        />
        </div>
      </div>
    </div>
  );
};

export default TemplatesOverviewPage;
