import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
import TemplateSelector from '../../components/templates/TemplateSelector';
import TemplateViewer from '../../components/templates/TemplateViewer';
import { RTLText, RTLFlex } from '../../components/common';
import {
  BookOpen,
  Store,
  Brain,
  ArrowLeft,
  Sparkles,
  Search,
  FileText
} from 'lucide-react';

const TemplatesOverviewPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [activeSection, setActiveSection] = useState<'landing' | 'browse' | 'marketplace' | 'ai' | 'template-selector'>('landing');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [previewTemplateId, setPreviewTemplateId] = useState<string>('');
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const handleSelectTemplate = (template: any) => {
    if (typeof template === 'string') {
      setSelectedTemplate(template);
    } else {
      navigate(`/dashboard/business-plans/new?template=${template.id}`);
    }
  };

  const handlePreviewTemplate = (template: any) => {
    if (typeof template === 'string') {
      setPreviewTemplateId(template);
      setShowPreviewModal(true);
    } else {
      console.log("Previewing template:", template);
    }
  };



  const renderTemplateSelector = () => (
    <div className="space-y-6">
      <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <button
          onClick={() => setActiveSection('landing')}
          className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
        >
          <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
          {t('common.back')}
        </button>
        <RTLText as="h2" className="text-2xl font-bold text-white">
          {t('templates.browseTemplates', 'Browse Templates')}
        </RTLText>
      </div>

      <TemplateSelector
        onSelectTemplate={handleSelectTemplate}
        selectedTemplate={selectedTemplate}
        showCategories={true}
        showFilters={true}
        showActionButtons={true}
        onPreviewTemplate={handlePreviewTemplate}
        onUseTemplate={(templateId) => {
          navigate(`/dashboard/business-plans/new?template=${templateId}`);
        }}
        onCreateTemplate={() => {
          navigate('/dashboard/templates/create');
        }}
      />

      {selectedTemplate && (
        <div className="bg-black/30 backdrop-blur-sm border border-green-500/50 rounded-lg p-4">
          <p className="text-green-400">
            ✅ Template selected: {selectedTemplate}
          </p>
          <p className="text-gray-300 text-sm mt-2">
            Click "Preview" to see template details or "Use Template" to start creating a business plan.
          </p>
        </div>
      )}
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'landing':
        return (
          <div className="space-y-8">
            {/* Welcome Section */}
            <div className="text-center">
              <h1 className="text-4xl font-bold text-white mb-4">
                {t('templates.welcome', 'Welcome to Templates')}
              </h1>
              <p className="text-gray-300 text-lg">
                {t('templates.welcomeDescription', 'Choose from our collection of professional business plan templates')}
              </p>
            </div>

            {/* Quick Access Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div
                onClick={() => setActiveSection('browse')}
                className="group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-blue-400/50 cursor-pointer transition-all duration-300"
              >
                <BookOpen className="w-8 h-8 text-blue-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <RTLText as="h3" className="text-lg font-semibold mb-2 text-white">
                  {t('templates.browseTemplates', 'Browse Templates')}
                </RTLText>
                <RTLText className="text-sm text-gray-300">
                  {t('templates.browseDescription', 'Explore our collection of business plan templates')}
                </RTLText>
              </div>

              <div
                onClick={() => setActiveSection('marketplace')}
                className="group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-green-400/50 cursor-pointer transition-all duration-300"
              >
                <Store className="w-8 h-8 text-green-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <RTLText as="h3" className="text-lg font-semibold mb-2 text-white">
                  {t('marketplace.templateMarketplace', 'Template Marketplace')}
                </RTLText>
                <RTLText className="text-sm text-gray-300">
                  {t('marketplace.description', 'Discover premium templates from our marketplace')}
                </RTLText>
              </div>

              <div
                onClick={() => setActiveSection('ai')}
                className="group p-6 bg-gray-800/50 rounded-xl border border-gray-700 hover:border-purple-400/50 cursor-pointer transition-all duration-300"
              >
                <Brain className="w-8 h-8 text-purple-400 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <RTLText as="h3" className="text-lg font-semibold mb-2 text-white">
                  {t('ai.smartTemplateRecommendations', 'AI Recommendations')}
                </RTLText>
                <RTLText className="text-sm text-gray-300">
                  {t('ai.recommendationsDescription', 'Get AI-powered template recommendations')}
                </RTLText>
              </div>
            </div>
          </div>
        );

      case 'browse':
      case 'template-selector':
        return renderTemplateSelector();

      case 'marketplace':
        return (
          <div className="space-y-6">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setActiveSection('landing')}
                className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                {t('common.back')}
              </button>
              <RTLText as="h2" className="text-2xl font-bold text-white">
                {t('marketplace.templateMarketplace', 'Template Marketplace')}
              </RTLText>
            </div>

            {/* Premium Templates Section */}
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 border border-yellow-500/30 rounded-lg p-6">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="text-xl font-bold text-yellow-400">Premium Templates</h3>
                  <span className="bg-yellow-500/20 text-yellow-300 px-3 py-1 rounded-full text-sm">Featured</span>
                </div>
                <p className="text-gray-300 mb-6">Professional templates with advanced features and AI integration</p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Premium Template 1 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:border-yellow-500/50 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-white">Advanced Tech Startup</h4>
                      <span className="bg-yellow-500 text-black px-2 py-1 rounded text-xs font-bold">PRO</span>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">Complete startup template with investor pitch deck, financial models, and market analysis</p>
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-2xl font-bold text-yellow-400">$29</span>
                      <span className="text-gray-500 text-sm">One-time</span>
                    </div>
                    <ul className="text-sm text-gray-300 space-y-1 mb-4">
                      <li>• 15 comprehensive sections</li>
                      <li>• AI-powered content generation</li>
                      <li>• Financial projection tools</li>
                      <li>• Investor pitch templates</li>
                    </ul>
                    <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-black font-semibold py-2 rounded-lg transition-colors">
                      Purchase Template
                    </button>
                  </div>

                  {/* Premium Template 2 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:border-yellow-500/50 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-white">Enterprise Business Plan</h4>
                      <span className="bg-yellow-500 text-black px-2 py-1 rounded text-xs font-bold">PRO</span>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">Enterprise-grade template for large-scale business planning with compliance features</p>
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-2xl font-bold text-yellow-400">$49</span>
                      <span className="text-gray-500 text-sm">One-time</span>
                    </div>
                    <ul className="text-sm text-gray-300 space-y-1 mb-4">
                      <li>• 20+ detailed sections</li>
                      <li>• Compliance checklists</li>
                      <li>• Risk assessment tools</li>
                      <li>• Multi-year projections</li>
                    </ul>
                    <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-black font-semibold py-2 rounded-lg transition-colors">
                      Purchase Template
                    </button>
                  </div>

                  {/* Premium Template 3 */}
                  <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:border-yellow-500/50 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-white">AI-Powered Bundle</h4>
                      <span className="bg-purple-500 text-white px-2 py-1 rounded text-xs font-bold">BUNDLE</span>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">Complete collection of all premium templates with AI assistance</p>
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-2xl font-bold text-purple-400">$99</span>
                      <span className="text-gray-500 text-sm line-through">$150</span>
                    </div>
                    <ul className="text-sm text-gray-300 space-y-1 mb-4">
                      <li>• All premium templates</li>
                      <li>• AI content generation</li>
                      <li>• Priority support</li>
                      <li>• Future updates included</li>
                    </ul>
                    <button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 rounded-lg transition-colors">
                      Get Bundle
                    </button>
                  </div>
                </div>
              </div>

              {/* Free Premium Trial */}
              <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 border border-green-500/30 rounded-lg p-6">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <h3 className="text-xl font-bold text-green-400">Free Premium Trial</h3>
                  <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm">Limited Time</span>
                </div>
                <p className="text-gray-300 mb-4">Try any premium template free for 7 days</p>
                <button className="bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-2 rounded-lg transition-colors">
                  Start Free Trial
                </button>
              </div>
            </div>
          </div>
        );

      case 'ai':
        return (
          <div className="space-y-6">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setActiveSection('landing')}
                className={`text-purple-400 hover:text-purple-300 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <ArrowLeft size={16} className={isRTL ? 'ml-1' : 'mr-1'} />
                {t('common.back')}
              </button>
              <RTLText as="h2" className="text-2xl font-bold text-white">
                {t('ai.smartTemplateRecommendations', 'AI Template Recommendations')}
              </RTLText>
            </div>

            {/* AI Recommendations Content */}
            <div className="space-y-6">
              {/* Quick Assessment */}
              <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-6">
                <div className={`flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Brain size={24} className="text-purple-400 mr-3" />
                  <h3 className="text-xl font-bold text-purple-400">AI Business Assessment</h3>
                </div>
                <p className="text-gray-300 mb-6">Answer a few questions to get personalized template recommendations</p>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">What type of business are you planning?</label>
                    <select className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none">
                      <option value="">Select business type...</option>
                      <option value="tech">Technology/Software</option>
                      <option value="ecommerce">E-commerce/Retail</option>
                      <option value="service">Service Business</option>
                      <option value="restaurant">Food & Beverage</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="consulting">Consulting</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">What's your experience level?</label>
                    <select className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none">
                      <option value="">Select experience...</option>
                      <option value="beginner">First-time entrepreneur</option>
                      <option value="intermediate">Some business experience</option>
                      <option value="advanced">Experienced entrepreneur</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">What's your primary goal?</label>
                    <select className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-purple-500 focus:outline-none">
                      <option value="">Select goal...</option>
                      <option value="funding">Seeking investment/funding</option>
                      <option value="planning">Internal planning & strategy</option>
                      <option value="loan">Bank loan application</option>
                      <option value="partnership">Partnership proposal</option>
                    </select>
                  </div>

                  <button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-lg transition-colors flex items-center justify-center">
                    <Sparkles size={20} className="mr-2" />
                    Get AI Recommendations
                  </button>
                </div>
              </div>

              {/* Recommended Templates */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
                <h3 className="text-xl font-bold text-white mb-4">Recommended for You</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* AI Recommendation 1 */}
                  <div className="bg-gradient-to-br from-blue-600/20 to-purple-600/20 border border-blue-500/30 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-white">Tech Startup Business Plan</h4>
                      <div className="flex items-center bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">
                        <Sparkles size={12} className="mr-1" />
                        95% Match
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">Perfect for technology startups seeking investment</p>
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-300">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        Matches your industry preference
                      </div>
                      <div className="flex items-center text-sm text-gray-300">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        Suitable for intermediate level
                      </div>
                      <div className="flex items-center text-sm text-gray-300">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        Optimized for funding goals
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg transition-colors text-sm">
                        Use Template
                      </button>
                      <button className="px-4 bg-gray-700 hover:bg-gray-600 text-gray-300 py-2 rounded-lg transition-colors text-sm">
                        Preview
                      </button>
                    </div>
                  </div>

                  {/* AI Recommendation 2 */}
                  <div className="bg-gradient-to-br from-green-600/20 to-blue-600/20 border border-green-500/30 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-white">E-commerce Business Plan</h4>
                      <div className="flex items-center bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">
                        <Sparkles size={12} className="mr-1" />
                        87% Match
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">Comprehensive template for online retail businesses</p>
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-300">
                        <span className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></span>
                        Alternative industry option
                      </div>
                      <div className="flex items-center text-sm text-gray-300">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        Beginner-friendly approach
                      </div>
                      <div className="flex items-center text-sm text-gray-300">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        Good for planning goals
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg transition-colors text-sm">
                        Use Template
                      </button>
                      <button className="px-4 bg-gray-700 hover:bg-gray-600 text-gray-300 py-2 rounded-lg transition-colors text-sm">
                        Preview
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Insights */}
              <div className="bg-gradient-to-r from-indigo-600/20 to-purple-600/20 border border-indigo-500/30 rounded-lg p-6">
                <h3 className="text-xl font-bold text-indigo-400 mb-4">AI Insights & Tips</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <h4 className="font-semibold text-white mb-2">Market Analysis Tip</h4>
                    <p className="text-gray-400 text-sm">Based on current trends, tech startups should focus on AI integration and sustainability in their market analysis.</p>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <h4 className="font-semibold text-white mb-2">Financial Projections</h4>
                    <p className="text-gray-400 text-sm">Consider including conservative, optimistic, and pessimistic scenarios in your financial projections for better investor confidence.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
          {/* Navigation Pills (only show when not on landing) */}
          {activeSection !== 'landing' && (
            <div className={`flex flex-wrap gap-2 p-4 bg-gray-800/50 rounded-lg border border-gray-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={() => setActiveSection('landing')}
                className={`px-4 py-2 bg-purple-600 text-white rounded-lg font-medium transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Sparkles size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('templates.home', 'Home')}
              </button>

              <button
                onClick={() => setActiveSection('browse')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  activeSection === 'browse' || activeSection === 'template-selector'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <BookOpen size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('templates.browseTemplates', 'Browse Templates')}
              </button>

              <button
                onClick={() => setActiveSection('marketplace')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  activeSection === 'marketplace'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <Store size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('marketplace.templateMarketplace', 'Marketplace')}
              </button>

              <button
                onClick={() => setActiveSection('ai')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  activeSection === 'ai'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}
                }`}
              >
                <Brain size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('ai.smartTemplateRecommendations', 'AI Recommendations')}
              </button>
            </div>
          )}

          {/* Main Content */}
          <div className="min-h-[600px]">
            {renderSectionContent()}
          </div>
        </div>

        {/* Template Viewer Modal */}
        <TemplateViewer
          templateId={previewTemplateId}
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          onUseTemplate={(templateId) => {
            navigate(`/dashboard/business-plans/new?template=${templateId}`);
            setShowPreviewModal(false);
          }}
          mode="preview"
        />
        </div>
      </div>
    </div>
  );
};

export default TemplatesOverviewPage;
