# Generated by Django 4.2.7 on 2025-07-12 07:46

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("incubator", "0014_business_plan_analytics"),
    ]

    operations = [
        migrations.RenameIndex(
            model_name="businessplancollaboration",
            new_name="business_pl_busines_31df19_idx",
            old_name="business_pl_busines_d3d3d3_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplancollaboration",
            new_name="business_pl_user_id_0a4925_idx",
            old_name="business_pl_user_id_e4e4e4_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplancollaboration",
            new_name="business_pl_action__f48885_idx",
            old_name="business_pl_action__f5f5f5_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplanexport",
            new_name="business_pl_busines_3b05bf_idx",
            old_name="business_pl_busines_161616_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplanexport",
            new_name="business_pl_user_id_92adfd_idx",
            old_name="business_pl_user_id_171717_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplanexport",
            new_name="business_pl_export__4f17e1_idx",
            old_name="business_pl_export__181818_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplansession",
            new_name="business_pl_busines_7c1631_idx",
            old_name="business_pl_busines_b8b8b8_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplansession",
            new_name="business_pl_user_id_787086_idx",
            old_name="business_pl_user_id_a1a1a1_idx",
        ),
        migrations.RenameIndex(
            model_name="businessplansession",
            new_name="business_pl_session_11af1f_idx",
            old_name="business_pl_session_c2c2c2_idx",
        ),
        migrations.AlterField(
            model_name="businessplananalytics",
            name="average_session_duration",
            field=models.DurationField(default=datetime.timedelta),
        ),
        migrations.AlterField(
            model_name="businessplananalytics",
            name="total_time_spent",
            field=models.DurationField(default=datetime.timedelta),
        ),
        migrations.AlterField(
            model_name="templatesuccessmetrics",
            name="average_completion_time",
            field=models.DurationField(default=datetime.timedelta),
        ),
    ]
