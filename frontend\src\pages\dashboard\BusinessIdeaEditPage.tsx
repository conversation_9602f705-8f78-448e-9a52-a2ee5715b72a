import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { BusinessIdea } from '../../services/incubatorApi';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/rtl';
import {
  fetchBusinessIdeaById,
  updateBusinessIdea
} from '../../store/incubatorSlice';
import { AppDispatch } from '../../store/store';

const BusinessIdeaEditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useDispatch<AppDispatch>();

  // Redux state
  const { user } = useAppSelector(state => state.auth);
  const {
    selectedBusinessIdea: businessIdea,
    isLoading: loading
  } = useAppSelector(state => state.incubator);

  // Local state
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    problem_statement: '',
    solution_description: '',
    target_audience: '',
    market_opportunity: '',
    business_model: '',
    current_stage: 'concept',
  });

  useEffect(() => {
    if (id) {
      fetchBusinessIdea();
    }
  }, [id]);

  const fetchBusinessIdea = async () => {
    if (!id) return;

    try {
      const idea = await dispatch(fetchBusinessIdeaById(parseInt(id))).unwrap();

      // Check if user owns this business idea
      if (idea.owner.id !== user?.id && !user?.is_staff && !user?.is_superuser) {
        setError(t('incubator.errors.notOwner', 'You can only edit your own business ideas'));
        return;
      }

      setFormData({
        title: idea.title,
        description: idea.description,
        problem_statement: idea.problem_statement,
        solution_description: idea.solution_description,
        target_audience: idea.target_audience,
        market_opportunity: idea.market_opportunity || '',
        business_model: idea.business_model || '',
        current_stage: idea.current_stage,
      });
    } catch (error) {
      console.error('Error fetching business idea:', error);
      setError(t('incubator.errors.failedToLoad', 'Failed to load business idea'));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !businessIdea) return;

    setSaving(true);
    setError(null);

    try {
      await dispatch(updateBusinessIdea({
        id: parseInt(id),
        ideaData: {
          ...formData,
          owner_id: businessIdea.owner_id, // Preserve original owner
        }
      })).unwrap();
      setSuccess(t('incubator.success.updated', 'Business idea updated successfully'));

      // Navigate back after success
      setTimeout(() => {
        navigate('/dashboard/business-ideas');
      }, 1500);
    } catch (error) {
      console.error('Error updating business idea:', error);
      setError(t('incubator.errors.failedToUpdate', 'Failed to update business idea'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center py-12">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>{t('common.loading', 'Loading...')}</span>
          </div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error && !businessIdea) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">{error}</div>
          <button
            onClick={() => navigate('/dashboard/business-ideas')}
            className="px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.goBack', 'Go Back')}
          </button>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => navigate('/dashboard/business-ideas')}
              className="p-2 rounded-lg hover:bg-indigo-800/50 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <RTLText as="h1" className="text-2xl font-bold">
                {t('incubator.editIdea', 'Edit Business Idea')}
              </RTLText>
              <RTLText as="div" className="text-gray-400 mt-1">
                {t('incubator.updateYourIdea', 'Update your business idea details')}
              </RTLText>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSave} className="space-y-6">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            {/* Title */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.title', 'Title')} *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
                placeholder={t('incubator.enterTitle', 'Enter your business idea title')}
                required
              />
            </div>

            {/* Description */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.description', 'Description')}
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('incubator.enterDescription', 'Provide a brief description of your business idea')}
              />
            </div>

            {/* Problem Statement */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.problemStatement', 'Problem Statement')} *
              </label>
              <textarea
                name="problem_statement"
                value={formData.problem_statement}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('incubator.enterProblem', 'What problem does your business solve?')}
                required
              />
            </div>

            {/* Solution Description */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.solutionDescription', 'Solution Description')} *
              </label>
              <textarea
                name="solution_description"
                value={formData.solution_description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('incubator.enterSolution', 'How does your business solve the problem?')}
                required
              />
            </div>

            {/* Target Audience */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.targetAudience', 'Target Audience')} *
              </label>
              <textarea
                name="target_audience"
                value={formData.target_audience}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('incubator.enterAudience', 'Who is your target audience?')}
                required
              />
            </div>

            {/* Market Opportunity */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.marketOpportunity', 'Market Opportunity')}
              </label>
              <textarea
                name="market_opportunity"
                value={formData.market_opportunity}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('incubator.enterMarket', 'Describe the market opportunity')}
              />
            </div>

            {/* Business Model */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.businessModel', 'Business Model')}
              </label>
              <textarea
                name="business_model"
                value={formData.business_model}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors resize-none"
                placeholder={t('incubator.enterModel', 'How will your business make money?')}
              />
            </div>

            {/* Current Stage */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2 font-medium">
                {t('incubator.currentStage', 'Current Stage')}
              </label>
              <select
                name="current_stage"
                value={formData.current_stage}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-indigo-900/50 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
              >
                <option value="concept">{t('incubator.stages.concept', 'Concept')}</option>
                <option value="planning">{t('incubator.stages.planning', 'Planning')}</option>
                <option value="development">{t('incubator.stages.development', 'Development')}</option>
                <option value="testing">{t('incubator.stages.testing', 'Testing')}</option>
                <option value="launch">{t('incubator.stages.launch', 'Launch')}</option>
                <option value="growth">{t('incubator.stages.growth', 'Growth')}</option>
              </select>
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="p-4 bg-red-900/50 border border-red-800 rounded-lg text-red-200">
              {error}
            </div>
          )}

          {success && (
            <div className="p-4 bg-green-900/50 border border-green-800 rounded-lg text-green-200">
              {success}
            </div>
          )}

          {/* Action Buttons */}
          <div className={`flex justify-end space-x-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              type="button"
              onClick={() => navigate('/dashboard/business-ideas')}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-medium transition-colors"
              disabled={saving}
            >
              {t('common.cancel', 'Cancel')}
            </button>
            <RTLFlex
              as="button"
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={saving}
              align="center"
            >
              {saving ? (
                <>
                  <Loader2 className={`w-5 h-5 animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save className={`w-5 h-5 ${language === 'ar' ? 'ml-2' : 'mr-2'}`} />
                  {t('common.save', 'Save Changes')}
                </>
              )}
            </RTLFlex>
          </div>
        </form>
      </div>
    </AuthenticatedLayout>
  );
};

export default BusinessIdeaEditPage;
