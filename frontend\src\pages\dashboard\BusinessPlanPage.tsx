import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link, useSearchParams } from 'react-router-dom';
import {
  FileText,
  Plus,
  RefreshCw,
  Check,
  AlertCircle,
  ArrowRight,
  Edit,
  Trash2,
  Download,
  Eye,
  BarChart2,
  Sparkles,
  Search,
  Filter,
  Calendar,
  TrendingUp,
  Users,
  Activity
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import {
  BusinessPlan,
  businessPlansAPI
} from '../../services/businessPlanApi';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import {
  BusinessPlanGenerator,
  AdvancedBusinessPlanGenerator,
  BusinessPlanEditor
} from '../../components/incubator';
import BusinessPlanAnalyticsDashboard from '../../components/analytics/BusinessPlanAnalyticsDashboard';
import AnalyticsTestComponent from '../../components/analytics/AnalyticsTestComponent';
// AuthDebugComponent removed - component not found
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

const BusinessPlanPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);
  const [searchParams] = useSearchParams();

  const [businessPlans, setBusinessPlans] = useState<BusinessPlan[]>([]);
  const [businessIdeas, setBusinessIdeas] = useState<BusinessIdea[]>([]);
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<number | null>(null);
  const [showGenerator, setShowGenerator] = useState(false);
  const [showAdvancedGenerator, setShowAdvancedGenerator] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [preselectedTemplate, setPreselectedTemplate] = useState<string | null>(null);

  // Check for template parameter and auto-open generator
  useEffect(() => {
    const templateParam = searchParams.get('template');
    if (templateParam) {
      setPreselectedTemplate(templateParam);
      setShowGenerator(true);
    }
  }, [searchParams]);

  // Fetch business plans and ideas
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch business ideas
        const ideas = await businessIdeasAPI.getBusinessIdeas();
        setBusinessIdeas(ideas);

        // Fetch business plans
        const plans = await businessPlansAPI.getPlans();
        setBusinessPlans(plans);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('businessPlan.failedToLoad'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCreatePlan = () => {
    console.log('🚀 CREATE: handleCreatePlan called');
    setShowGenerator(true);
    setShowAdvancedGenerator(false);
    console.log('🚀 CREATE: State updated - showGenerator:', true, 'showAdvancedGenerator:', false);
  };

  const handleCreateAdvancedPlan = () => {
    console.log('🚀 CREATE: handleCreateAdvancedPlan called');
    setShowAdvancedGenerator(true);
    setShowGenerator(false);
    console.log('🚀 CREATE: State updated - showAdvancedGenerator:', true, 'showGenerator:', false);
  };

  const handlePlanCreated = (planId: number) => {
    // Refresh the list of plans
    businessPlansAPI.getPlans().then(plans => {
      setBusinessPlans(plans);
      setShowGenerator(false);
      setShowAdvancedGenerator(false);

      // Navigate to the new plan
      navigate(`/dashboard/business-plans/${planId}`);
    });
  };

  const handleDeletePlan = async (planId: number) => {
    console.log('🗑️ DELETE: handleDeletePlan called with planId:', planId);

    if (!window.confirm(t('businessPlan.confirmDelete'))) {
      console.log('🗑️ DELETE: User cancelled deletion');
      return;
    }

    console.log('🗑️ DELETE: User confirmed deletion, proceeding...');
    try {
      await businessPlansAPI.deletePlan(planId);
      console.log('🗑️ DELETE: Plan deleted successfully');

      // Refresh the list of plans
      const plans = await businessPlansAPI.getPlans();
      setBusinessPlans(plans);
      console.log('🗑️ DELETE: Plans list refreshed');
    } catch (err) {
      console.error('🗑️ DELETE: Error deleting business plan:', err);
      setError(t('businessPlan.failedToDelete'));
    }
  };

  const handleRefresh = async () => {
    setLoading(true);
    setError(null);
    try {
      const [ideas, plans] = await Promise.all([
        businessIdeasAPI.getBusinessIdeas(),
        businessPlansAPI.getPlans()
      ]);
      setBusinessIdeas(ideas);
      setBusinessPlans(plans);
    } catch (err) {
      console.error('Error refreshing data:', err);
      setError(t('businessPlan.failedToLoad'));
    } finally {
      setLoading(false);
    }
  };

  // Filter business plans based on search and status
  const filteredPlans = businessPlans.filter(plan => {
    const matchesSearch = plan.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || plan.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // If viewing a specific plan
  if (id) {
    return (
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
          <BusinessPlanEditor
            businessPlanId={parseInt(id)}
            onBack={() => navigate('/dashboard/business-plans')}
          />
        </div>
                </div>
        </div>
      </div>
    );
  }

  return (
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              {t('businessPlan.title')}
            </h1>
            <p className="text-gray-300">
              {t('businessPlan.description')}
            </p>
          </div>
          <div className={`flex items-center space-x-3 mt-4 sm:mt-0 ${isRTL ? 'space-x-reverse' : ''}`}>
            <button
              onClick={() => setShowAnalytics(!showAnalytics)}
              className={`p-2 rounded-lg transition-colors ${showAnalytics ? 'bg-purple-600 hover:bg-purple-700' : 'bg-indigo-900/50 hover:bg-indigo-800/50'}`}
              title={t('analytics.viewAnalytics', 'View Analytics')}
            >
              <Activity size={18} className="text-gray-300" />
            </button>
            <button
              onClick={handleRefresh}
              className="p-2 bg-indigo-900/50 hover:bg-indigo-800/50 rounded-lg transition-colors"
              title={t('common.refresh')}
            >
              <RefreshCw size={18} className="text-gray-300" />
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <FileText size={24} className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="text-sm text-gray-400">{t('businessPlan.totalPlans')}</p>
                <p className="text-2xl font-bold text-white">{businessPlans.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Check size={24} className={`text-green-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="text-sm text-gray-400">{t('businessPlan.completedPlans')}</p>
                <p className="text-2xl font-bold text-white">
                  {businessPlans.filter(plan => plan.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Edit size={24} className={`text-blue-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="text-sm text-gray-400">{t('businessPlan.draftPlans')}</p>
                <p className="text-2xl font-bold text-white">
                  {businessPlans.filter(plan => plan.status === 'draft').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <TrendingUp size={24} className={`text-orange-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <p className="text-sm text-gray-400">{t('businessPlan.avgCompletion')}</p>
                <p className="text-2xl font-bold text-white">
                  {businessPlans.length > 0
                    ? Math.round(businessPlans.reduce((acc, plan) => acc + (plan.completion_percentage || 0), 0) / businessPlans.length)
                    : 0}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-red-500/30">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertCircle size={20} className={`text-red-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} />
              <div>
                <h3 className="text-lg font-semibold text-white mb-1">
                  {t('common.error')}
                </h3>
                <p className="text-gray-300">{error}</p>
              </div>
            </div>
            <button
              onClick={handleRefresh}
              className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white font-medium transition-colors"
            >
              {t('common.tryAgain')}
            </button>
          </div>
        )}

        {/* Analytics Dashboard */}
        {showAnalytics && !showGenerator && !showAdvancedGenerator && (
          <div className="space-y-6">
            <BusinessPlanAnalyticsDashboard />
            <AnalyticsTestComponent />
            {/* AuthDebugComponent removed - component not found */}
          </div>
        )}

        {/* Create New Plan Buttons */}
        {!showGenerator && !showAdvancedGenerator && !showAnalytics && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <h3 className="text-lg font-semibold text-white mb-4">
              {t('businessPlan.createNewPlan')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <button
                onClick={handleCreatePlan}
                className={`p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white flex items-center justify-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Plus size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('businessPlan.createStandard')}
              </button>

              <button
                onClick={handleCreateAdvancedPlan}
                className={`p-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg text-white flex items-center justify-center transition-all duration-300 hover:shadow-glow ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Sparkles size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('businessPlan.createAIPowered')}
              </button>

              <Link
                to="/dashboard/templates"
                className={`p-4 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white flex items-center justify-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <FileText size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('businessPlan.manageTemplates')}
              </Link>
            </div>
            <p className="text-sm text-gray-400">
              {t('businessPlan.createDescription')}
            </p>
          </div>
        )}

        {/* Search and Filter */}
        {!showGenerator && !showAdvancedGenerator && !showAnalytics && businessPlans.length > 0 && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Search */}
              <div className="relative">
                <Search size={20} className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`} />
                <input
                  type="text"
                  placeholder={t('businessPlan.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500`}
                />
              </div>

              {/* Status Filter */}
              <div className="relative">
                <Filter size={20} className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`} />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500`}
                >
                  <option value="all">{t('businessPlan.allStatuses')}</option>
                  <option value="draft">{t('businessPlan.draft')}</option>
                  <option value="in_progress">{t('businessPlan.inProgress')}</option>
                  <option value="completed">{t('businessPlan.completed')}</option>
                  <option value="published">{t('businessPlan.published')}</option>
                </select>
              </div>
            </div>
          </div>
        )}

      {/* Standard Business Plan Generator */}
      {showGenerator && (
        <div className="mb-6">
          <BusinessPlanGenerator
            businessIdeaId={selectedBusinessIdea}
            preselectedTemplate={preselectedTemplate}
            onSuccess={handlePlanCreated}
          />

          <div className="mt-4">
            <button
              onClick={() => {
                setShowGenerator(false);
                setPreselectedTemplate(null);
                // Clear the template parameter from URL
                const newSearchParams = new URLSearchParams(searchParams);
                newSearchParams.delete('template');
                navigate(`/dashboard/business-plans/new?${newSearchParams.toString()}`, { replace: true });
              }}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {t('common.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* Advanced AI-Powered Business Plan Generator */}
      {showAdvancedGenerator && (
        <div className="mb-6">
          <AdvancedBusinessPlanGenerator
            businessIdeaId={selectedBusinessIdea}
            onSuccess={handlePlanCreated}
          />

          <div className="mt-4">
            <button
              onClick={() => setShowAdvancedGenerator(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {t('common.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* Business Plans List */}
      {!showGenerator && !showAdvancedGenerator && !showAnalytics && (
        <div>
          {loading ? (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-12 border border-white/20">
              <div className="text-center">
                <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {t('businessPlan.loadingPlans')}
                </h3>
                <p className="text-gray-400">
                  {t('businessPlan.loadingPlansDescription')}
                </p>
              </div>
            </div>
          ) : filteredPlans.length === 0 ? (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-12 border border-white/20 text-center">
              <FileText size={48} className="mx-auto mb-4 text-gray-500" />
              <h3 className="text-xl font-bold text-white mb-2">
                {businessPlans.length === 0 ? t('businessPlan.noPlansYet') : t('businessPlan.noMatchingPlans')}
              </h3>
              <p className="text-gray-400 mb-6">
                {businessPlans.length === 0
                  ? t('businessPlan.createFirstPlan')
                  : t('businessPlan.tryDifferentSearch')
                }
              </p>
              {businessPlans.length === 0 && (
                <button
                  onClick={handleCreatePlan}
                  className={`px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg text-white inline-flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <Plus size={18} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('businessPlan.createFirstPlanButton')}
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredPlans.map(plan => {
                const businessIdea = businessIdeas.find(idea => idea.id === plan.business_idea);

                return (
                  <div
                    key={plan.id}
                    className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:border-purple-500/50 transition-all duration-300"
                  >
                    <div className={`flex items-start justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <FileText size={20} className={`text-purple-400 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                        <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <h3 className="text-lg font-semibold text-white mb-1">{plan.title}</h3>
                          {businessIdea && (
                            <p className="text-sm text-gray-400">
                              {t('businessPlan.basedOn')}: {businessIdea.title}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                        <button
                          onClick={() => navigate(`/dashboard/business-plans/${plan.id}`)}
                          className="p-2 text-gray-400 hover:text-white transition-colors"
                          title={t('common.edit')}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeletePlan(plan.id)}
                          className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                          title={t('common.delete')}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>

                    {/* Status Badge */}
                    <div className="mb-4">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                        plan.status === 'completed'
                          ? 'bg-green-600/20 text-green-300 border border-green-600/30'
                          : plan.status === 'in_progress'
                          ? 'bg-blue-600/20 text-blue-300 border border-blue-600/30'
                          : 'bg-gray-600/20 text-gray-300 border border-gray-600/30'}
                      }`}>
                        {t(`businessPlan.${plan.status}`)}
                      </span>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className={`flex justify-between text-xs text-gray-400 mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <span>{t('businessPlan.completion')}: {plan.completion_percentage || 0}%</span>
                        <span>{new Date(plan.updated_at).toLocaleDateString()}</span>
                      </div>
                      <div className="w-full bg-indigo-950/50 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${plan.completion_percentage || 0}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`flex items-center text-xs text-gray-400 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <Calendar size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                        {t('businessPlan.lastUpdated')}: {new Date(plan.updated_at).toLocaleDateString()}
                      </div>

                      <button
                        onClick={() => navigate(`/dashboard/business-plans/${plan.id}`)}
                        className={`text-purple-400 hover:text-purple-300 text-sm flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                      >
                        {t('businessPlan.viewPlan')}
                        <ArrowRight size={14} className={`${isRTL ? 'mr-1' : 'ml-1'}`} />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
        )}
      </div>
  );
};

export default BusinessPlanPage;
