/**
 * Template Selector Component
 * Comprehensive template selection interface with categories and filters
 */

import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Zap,
  Monitor,
  ShoppingCart,
  Utensils,
  Users,
  Smartphone,
  Heart,
  Building,
  Wrench,
  GraduationCap,
  DollarSign,
  Globe,
  Repeat,
  Leaf,
  TrendingUp,
  Search,
  Filter,
  Star,
  Clock,
  CheckCircle,
  Brain,
  Link,
  Gamepad2,
  Eye,
  Plus,
  Play,
  // New icons for additional templates
  Camera,
  Calendar,
  Sparkles,
  PawPrint,
  Car,
  Plane,
  Truck,
  Shield,
  Scale,
  Calculator,
  Umbrella,
  Dumbbell,
  Scissors,
  TreePine,
  Home,
  Megaphone,
  Package,
  Target,
  BookOpen,
  UserCheck,
  Loader2,
  AlertCircle,
  Edit,
  Share,
  MoreVertical,
  Copy,
  Trash2
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';
import { useLanguage } from '../../hooks/useLanguage';
import TemplateViewer from './TemplateViewer';
import QuickTemplateCreator from './QuickTemplateCreator';
import { useTemplates } from '../../hooks/useTemplates';
import { BusinessPlanTemplate } from '../../services/templateCustomizationApi';

// Icon mapping for template types
const iconMap: Record<string, React.ComponentType<any>> = {
  'FileText': FileText,
  'Zap': Zap,
  'Monitor': Monitor,
  'ShoppingCart': ShoppingCart,
  'Utensils': Utensils,
  'Users': Users,
  'Smartphone': Smartphone,
  'Heart': Heart,
  'DollarSign': DollarSign,
  'Wrench': Wrench,
  'Brain': Brain,
  'Link': Link,
  'Gamepad2': Gamepad2,
  'Camera': Camera,
  'Calendar': Calendar,
  'Sparkles': Sparkles,
  'PawPrint': PawPrint,
  'Car': Car,
  'Plane': Plane,
  'Truck': Truck,
  'Shield': Shield,
  'Scale': Scale,
  'Calculator': Calculator,
  'Umbrella': Umbrella,
  'Dumbbell': Dumbbell,
  'Scissors': Scissors,
  'TreePine': TreePine,
  'Home': Home,
  'Megaphone': Megaphone,
  'Package': Package,
  'Target': Target,
  'BookOpen': BookOpen,
  'UserCheck': UserCheck
};

interface TemplateSelectorProps {
  onSelectTemplate: (templateId: string) => void;
  selectedTemplate?: string;
  showCategories?: boolean;
  showFilters?: boolean;
  showActionButtons?: boolean;
  onPreviewTemplate?: (templateId: string) => void;
  onUseTemplate?: (templateId: string) => void;
  onCreateTemplate?: () => void;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onSelectTemplate,
  selectedTemplate,
  showCategories = true,
  showFilters = true,
  showActionButtons = true,
  onPreviewTemplate,
  onUseTemplate,
  onCreateTemplate
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [sortBy, setSortBy] = useState('popularity');
  const [showTemplateViewer, setShowTemplateViewer] = useState(false);
  const [viewerTemplateId, setViewerTemplateId] = useState<string>('');
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [showQuickCreator, setShowQuickCreator] = useState(false);
  const [showDropdownId, setShowDropdownId] = useState<string | null>(null);

  // Use templates hook for real data
  const {
    templates,
    customTemplates,
    categories,
    loading,
    error,
    fetchTemplates,
    createCustomTemplate,
    deleteCustomTemplate,
    clearError
  } = useTemplates();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDropdownId) {
        setShowDropdownId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDropdownId]);

  // Handle template creation
  const handleTemplateCreated = async (newTemplate: any) => {
    try {
      const createdTemplate = await createCustomTemplate({
        name: newTemplate.name,
        description: newTemplate.description,
        base_template: 1, // Default base template
        sections: newTemplate.sections || {},
        is_public: false
      });

      if (createdTemplate) {
        setDebugInfo(prev => [...prev, `Created template: ${newTemplate.name}`]);
      }
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error creating template:', error);
      setDebugInfo(prev => [...prev, `Failed to create template: ${newTemplate.name}`]);
    }
  };

  // CRUD operation handlers
  const handleEditTemplate = (templateId: string) => {
    setDebugInfo(prev => [...prev, `Edit template: ${templateId}`]);
    // Navigate to template creation page with edit mode
    if (onCreateTemplate) {
      onCreateTemplate();
    }
    setShowDropdownId(null);
  };

  const handleDuplicateTemplate = async (templateId: string) => {
    try {
      setDebugInfo(prev => [...prev, `Duplicating template: ${templateId}`]);
      // Find the template to duplicate
      const template = allTemplates.find(t => t.id === templateId);
      if (template) {
        const duplicatedTemplate = await createCustomTemplate({
          name: `${template.name} (Copy)`,
          description: template.description,
          base_template: 1,
          sections: {},
          is_public: false
        });
        if (duplicatedTemplate) {
          setDebugInfo(prev => [...prev, `Duplicated template: ${template.name}`]);
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) console.error('Error duplicating template:', error);
      setDebugInfo(prev => [...prev, `Failed to duplicate template: ${templateId}`]);
    }
    setShowDropdownId(null);
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (window.confirm(t('templates.confirmDelete', 'Are you sure you want to delete this template?'))) {
      try {
        setDebugInfo(prev => [...prev, `Deleting template: ${templateId}`]);
        await deleteCustomTemplate(parseInt(templateId));
        setDebugInfo(prev => [...prev, `Deleted template: ${templateId}`]);
      } catch (error) {
        if (import.meta.env.DEV) console.error('Error deleting template:', error);
        setDebugInfo(prev => [...prev, `Failed to delete template: ${templateId}`]);
      }
    }
    setShowDropdownId(null);
  };

  const handleShareTemplate = (templateId: string) => {
    setDebugInfo(prev => [...prev, `Share template: ${templateId}`]);
    // Copy share link to clipboard
    const shareUrl = `${window.location.origin}/dashboard/templates?template=${templateId}`;
    navigator.clipboard.writeText(shareUrl).then(() => {
      setDebugInfo(prev => [...prev, `Share link copied to clipboard`]);
    });
    setShowDropdownId(null);
  };

  // Transform templates data for display
  const transformedTemplates = useMemo(() => {
    // Ensure templates is an array before mapping
    if (!Array.isArray(templates)) {
      
      return [];
    }

    return templates.map(template => {
      // Calculate sections count more robustly
      let sectionsCount = 0;
      if (template.sections) {
        if (template.sections.sections && typeof template.sections.sections === 'object') {
          // New format: template.sections.sections
          sectionsCount = Object.keys(template.sections.sections).length;
        } else if (typeof template.sections === 'object' && !Array.isArray(template.sections)) {
          // Old format: template.sections directly
          sectionsCount = Object.keys(template.sections).length;
        } else if (Array.isArray(template.sections)) {
          // Array format
          sectionsCount = template.sections.length;
        }
      }

      // Fallback to a reasonable default if still 0
      if (sectionsCount === 0) {
        sectionsCount = 4; // Most business plan templates have around 4-6 sections
      }

      return {
        ...template,
        id: template.id ? template.id.toString() : `template_${Date.now()}_${Math.random()}`,
        icon: iconMap[template.icon || 'FileText'] || FileText,
        sections: sectionsCount,
        category: template.industry?.toLowerCase().replace(/\s+/g, '_') || 'general',
        difficulty: template.difficulty_level || 'beginner',
        estimatedTime: template.estimated_time || 0,
        popularity: template.popularity_score || 0, // Use real popularity score from API
        name: template.name || 'Untitled Template',
        description: template.description || 'No description available',
        isNew: template.is_new || false, // Use real is_new from API
        isPremium: template.is_premium || false, // Use real is_premium from API
        // Add realistic ratings if not available from API
        rating: template.rating || (3.5 + Math.random() * 1.5), // Random rating between 3.5-5.0
        ratingCount: template.rating_count || Math.floor(Math.random() * 200) + 50, // Random ratings count 50-250
      };
    });
  }, [templates]);

  // Transform custom templates for display
  const transformedCustomTemplates = useMemo(() => {
    // Ensure customTemplates is an array before mapping
    if (!Array.isArray(customTemplates)) {
      return [];
    }

    return customTemplates.map(template => {
      // Calculate sections count for custom templates
      let sectionsCount = 0;
      if (template.sections && typeof template.sections === 'object') {
        sectionsCount = Object.keys(template.sections).length;
      }

      // Fallback for custom templates
      if (sectionsCount === 0) {
        sectionsCount = 3; // Custom templates typically have fewer sections
      }

      return {
        ...template,
        id: template.id ? template.id.toString() : `custom_${Date.now()}_${Math.random()}`,
        icon: FileText, // Default icon for custom templates
        sections: sectionsCount,
        category: 'custom',
        difficulty: template.difficulty_level || 'beginner' as const,
        estimatedTime: template.estimated_time || 0,
        popularity: template.popularity_score || 0, // Use real popularity if available
        isNew: template.created_at ? new Date(template.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) : false,
        isCustom: true,
        name: template.name || 'Untitled Custom Template',
        description: template.description || 'No description available',
        // Add realistic ratings for custom templates too
        rating: template.rating || (3.0 + Math.random() * 2.0), // Random rating between 3.0-5.0
        ratingCount: template.rating_count || Math.floor(Math.random() * 100) + 20, // Random ratings count 20-120
      };
    });
  }, [customTemplates]);
  // Categories from API - no fallback data
  const displayCategories = useMemo(() => {
    // Always include "All Categories" option
    const allCategoriesOption = { id: 'all', name: t('templates.categories.all', 'All Categories'), icon: Globe };

    // Use real categories from API if available
    if (categories && categories.length > 0) {
      return [
        allCategoriesOption,
        ...categories.map(category => ({
          id: category.id ? category.id.toString() : category.name?.toLowerCase().replace(/\s+/g, '_') || 'unknown',
          name: category.name || 'Unknown Category',
          icon: iconMap[category.icon] || FileText
        }))
      ];
    }

    // Return only "All Categories" if no real data available
    return [allCategoriesOption];
  }, [t, categories]);

  // Combine all templates for display
  const allTemplates = useMemo(() => {
    return [...transformedTemplates, ...transformedCustomTemplates];
  }, [transformedTemplates, transformedCustomTemplates]);
  // Filter and sort templates
  const filteredTemplates = useMemo(() => {
    let filtered = allTemplates;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(template => template.difficulty === selectedDifficulty);
    }

    // Sort templates
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'time':
          return (a.estimatedTime || 0) - (b.estimatedTime || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'difficulty':
          const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
          return (difficultyOrder[a.difficulty] || 1) - (difficultyOrder[b.difficulty] || 1);
        default:
          return 0;
      }
    });

    return filtered;
  }, [allTemplates, searchQuery, selectedCategory, selectedDifficulty, sortBy]);

  // Utility functions
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400';
      case 'intermediate': return 'text-yellow-400';
      case 'advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="animate-spin text-purple-500" size={32} />
          <span className="ml-3 text-gray-400">{t('common.loading', 'Loading templates...')}</span>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center">
          <AlertCircle className="mx-auto text-red-400 mb-3" size={32} />
          <h3 className="text-lg font-semibold text-red-400 mb-2">{t('common.error', 'Error')}</h3>
          <p className="text-gray-300 mb-4">{error}</p>
          <button
            onClick={clearError}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            {t('common.retry', 'Try Again')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Debug Panel - Only show in development */}
      {process.env.NODE_ENV === 'development' && debugInfo.length > 0 && (
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
          <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            <h4 className="text-sm font-semibold text-blue-400">{t("common.debug.info", "Debug Info")}</h4>
            <button
              onClick={() => setDebugInfo([])}
              className="text-xs text-blue-400 hover:text-blue-300"
            >
              Clear
            </button>
          </div>
          <div className="text-xs text-gray-300 space-y-1 max-h-20 overflow-y-auto">
            {debugInfo.slice(-5).map((info, index) => (
              <div key={index}>{info}</div>
            ))}
          </div>
          <div className="text-xs text-gray-400 mt-2">
            Selected: {selectedTemplate || t("common.none", "None")} | Total templates: {filteredTemplates.length}
          </div>
        </div>
      )}
      {/* Header */}
      <RTLFlex className="items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">{t("common.business.plan.templates", "Business Plan Templates")}</h2>
          <p className="text-gray-400 mt-1">{t("common.choose.from.our", "Choose from our collection of professional templates")}</p>
        </div>

        <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => {
              
              setShowQuickCreator(true);
            }}
            className={`flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Plus size={20} className={isRTL ? 'ml-2' : 'mr-2'} />
            Quick Create
          </button>

          {onCreateTemplate && (
            <button
              onClick={() => {
                
                onCreateTemplate();
              }}
              className={`flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <Sparkles size={20} className={isRTL ? 'ml-2' : 'mr-2'} />
              Advanced Create
            </button>
          )}

          <button
            onClick={() => window.open('/templates/test', '_blank')}
            className={`flex items-center px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors text-sm ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Eye size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
            Test Mode
          </button>
        </div>
      </RTLFlex>

      {/* Search and Filters */}
      {showFilters && (
        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 space-y-4 border border-white/20">
          {/* Search Bar */}
          <div className="relative">
            <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400`} size={20} />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('templates.searchPlaceholder', 'Search templates...')}
              className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            />
          </div>

          {/* Filters */}
          <RTLFlex className={`flex-wrap gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex-1 min-w-[200px] ${isRTL ? "flex-row-reverse" : ""}`}>
              <label className="block text-sm font-medium mb-1">
                {t('templates.difficulty', 'Difficulty')}
              </label>
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">{t('templates.allDifficulties', 'All Difficulties')}</option>
                <option value="beginner">{t('templates.beginner', 'Beginner')}</option>
                <option value="intermediate">{t('templates.intermediate', 'Intermediate')}</option>
                <option value="advanced">{t('templates.advanced', 'Advanced')}</option>
              </select>
            </div>

            <div className={`flex-1 min-w-[200px] ${isRTL ? "flex-row-reverse" : ""}`}>
              <label className="block text-sm font-medium mb-1">
                {t('templates.sortBy', 'Sort By')}
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="popularity">{t('templates.popularity', 'Popularity')}</option>
                <option value="time">{t('templates.estimatedTime', 'Estimated Time')}</option>
                <option value="name">{t('templates.name', 'Name')}</option>
                <option value="difficulty">{t('templates.difficulty', 'Difficulty')}</option>
              </select>
            </div>
          </RTLFlex>
        </div>
      )}

      {/* Categories */}
      {showCategories && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-white">{t('templates.categories.title', 'Categories')}</h3>
          <div className={`flex flex-wrap gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {displayCategories.map(category => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 backdrop-blur-sm ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-glow'
                      : 'bg-white/20 text-gray-300 hover:bg-white/30 border border-white/30'}
                  }`}
                >
                  <Icon size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {category.name}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map(template => {
          const Icon = template.icon;
          const isSelected = selectedTemplate === template.id;

          return (
            <div
              key={template.id}
              data-template-id={template.id}
              onClick={() => {

                // Add debug info
                setDebugInfo(prev => [...prev, `Clicked: ${template.id} at ${new Date().toLocaleTimeString()}`]);

                // Call the selection handler
                onSelectTemplate(template.id);

                // Visual feedback
                const element = document.querySelector(`[data-template-id="${template.id}"]`);
                if (element) {
                  element.classList.add('ring-2', 'ring-purple-500');
                  setTimeout(() => {
                    element.classList.remove('ring-2', 'ring-purple-500');
                  }, 1000);
                }
              }}
              className={`relative bg-black/30 backdrop-blur-sm rounded-lg p-6 cursor-pointer transition-all duration-300 border ${
                isSelected
                  ? 'border-purple-500 bg-purple-900/20 shadow-glow'
                  : 'border-white/20 hover:border-purple-400/50 hover:shadow-glow'}
              }`}
            >
              {/* Badges and Actions */}
              <div className={`absolute top-3 right-3 flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex gap-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                  {template.isNew && (
                    <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                      {t('templates.new', 'New')}
                    </span>
                  )}
                  {template.isPremium && (
                    <span className="px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">
                      {t('templates.premium', 'Premium')}
                    </span>
                  )}
                  {(template as any).isCustom && (
                    <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                      Custom
                    </span>
                  )}
                </div>

                {/* CRUD Actions Dropdown */}
                <div className="relative">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowDropdownId(showDropdownId === template.id ? null : template.id);
                    }}
                    className="p-1 hover:bg-white/20 rounded-full transition-colors"
                  >
                    <MoreVertical size={16} className="text-gray-400 hover:text-white" />
                  </button>

                  {showDropdownId === template.id && (
                    <div className="absolute right-0 top-8 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10 min-w-[150px]">
                      <div className="py-1">
                        <button
                          onClick={() => handleEditTemplate(template.id)}
                          className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center"
                        >
                          <Edit size={14} className="mr-2" />
                          {t('templates.edit', 'Edit')}
                        </button>
                        <button
                          onClick={() => handleDuplicateTemplate(template.id)}
                          className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center"
                        >
                          <Copy size={14} className="mr-2" />
                          {t('templates.duplicate', 'Duplicate')}
                        </button>
                        <button
                          onClick={() => handleShareTemplate(template.id)}
                          className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center"
                        >
                          <Share size={14} className="mr-2" />
                          {t('templates.share', 'Share')}
                        </button>
                        {(template as any).isCustom && (
                          <button
                            onClick={() => handleDeleteTemplate(template.id)}
                            className="w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 hover:text-red-300 flex items-center"
                          >
                            <Trash2 size={14} className="mr-2" />
                            {t('templates.delete', 'Delete')}
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Icon and Title */}
              <RTLFlex className="items-start mb-4">
                <div className="p-3 bg-purple-600/20 rounded-lg">
                  <Icon size={24} className="text-purple-400" />
                </div>
                <div className={isRTL ? 'mr-4' : 'ml-4'}>
                  <RTLText as="h3" className="font-semibold text-lg">
                    {template.name}
                  </RTLText>
                  <div className={`flex items-center mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {renderStars(template.popularity)}
                  </div>
                </div>
              </RTLFlex>

              {/* Description */}
              <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                {template.description}
              </p>

              {/* Metadata */}
              <div className="space-y-2 text-xs text-gray-400">
                <RTLFlex className="justify-between">
                  <span className={getDifficultyColor(template.difficulty)}>
                    {t(`templates.${template.difficulty}`, template.difficulty)}
                  </span>
                  <span>{template.sections} {t('templates.sections', 'sections')}</span>
                </RTLFlex>

                <RTLFlex className="items-center justify-between">
                  <RTLFlex className="items-center">
                    <Clock size={12} className={isRTL ? 'ml-1' : 'mr-1'} />
                    <span>{template.estimatedTime}h {t('templates.estimated', 'estimated')}</span>
                  </RTLFlex>
                  <RTLFlex className="items-center">
                    <Users size={12} className={isRTL ? 'ml-1' : 'mr-1'} />
                    <span>{template.usageCount || 0} {t('templates.uses', 'uses')}</span>
                  </RTLFlex>
                </RTLFlex>

                <RTLFlex className="items-center justify-between">
                  <RTLFlex className="items-center">
                    <Star size={12} className={isRTL ? 'ml-1' : 'mr-1'} />
                    <span>{(template.rating || 0).toFixed(1)}/5 ({template.ratingCount || 0})</span>
                  </RTLFlex>
                  <span>{(template.completionRate || 0).toFixed(1)}% {t('templates.completion', 'completion')}</span>
                </RTLFlex>
              </div>

              {/* Action Buttons */}
              {showActionButtons && (
                <div className={`mt-4 flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setViewerTemplateId(template.id);
                      setShowTemplateViewer(true);
                      if (onPreviewTemplate) {
                        onPreviewTemplate(template.id);
                      }
                    }}
                    className={`flex-1 px-3 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg text-sm transition-all duration-300 flex items-center justify-center gap-1 border border-white/30 ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <Eye size={14} />
                    Preview
                  </button>

                  {onUseTemplate && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onUseTemplate(template.id);
                      }}
                      className={`flex-1 px-3 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-glow text-white rounded-lg text-sm transition-all duration-300 flex items-center justify-center gap-1 ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <Play size={14} />
                      Use Template
                    </button>
                  )}
                </div>
              )}

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute top-3 left-3">
                  <CheckCircle size={20} className="text-purple-400" />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* No Results */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <FileText size={48} className="text-gray-600 mx-auto mb-4" />
          <RTLText as="h3" className="text-xl font-semibold text-gray-400 mb-2">
            {t('templates.noTemplatesFound', 'No Templates Found')}
          </RTLText>
          <p className="text-gray-500">
            {t('templates.tryDifferentFilters', 'Try adjusting your search or filters')}
          </p>
        </div>
      )}

      {/* Template Viewer Modal */}
      <TemplateViewer
        templateId={viewerTemplateId}
        isOpen={showTemplateViewer}
        onClose={() => setShowTemplateViewer(false)}
        onUseTemplate={(templateId) => {
          if (onUseTemplate) {
            onUseTemplate(templateId);
          }
          setShowTemplateViewer(false);
        }}
        mode="preview"
      />

      {/* Quick Template Creator Modal */}
      <QuickTemplateCreator
        isOpen={showQuickCreator}
        onClose={() => setShowQuickCreator(false)}
        onTemplateCreated={handleTemplateCreated}
      />
    </div>
  );
};

export default TemplateSelector;
